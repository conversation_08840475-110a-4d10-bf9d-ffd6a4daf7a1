<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class StaffShiftSeeder extends Seeder
{
    /**
     * Run the database seeds to generate one week of dummy shifts.
     */
    public function run(): void
    {
        // Generate sample staff IDs
        $staffIds = [
            Str::uuid()->toString(),
            Str::uuid()->toString(),
            Str::uuid()->toString()
        ];

        // Define shift roles
        $roles = ['manager', 'cashier', 'floor_staff', 'kitchen_staff'];

        // Get current date and generate shifts for next 7 days
        $startDate = Carbon::now()->startOfDay();
        $storeId = 1; // Fixed store_id as per requirement

        foreach ($staffIds as $staffId) {
            for ($i = 0; $i < 7; $i++) {
                $shiftDate = $startDate->copy()->addDays($i);
                $role = $roles[array_rand($roles)];

                // Morning shift: 9:00 - 13:00
                DB::table('staff_shifts')->insert([
                    'store_id' => $storeId,
                    'staff_id' => $staffId,
                    'role_col' => $role,
                    'start_at' => $shiftDate->copy()->setTime(9, 0),
                    'end_at' => $shiftDate->copy()->setTime(13, 0),
                    'note' => "Morning shift for {$role}",
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

                // Afternoon shift for some days (14:00 - 18:00)
                if ($i % 2 === 0) {
                    DB::table('staff_shifts')->insert([
                        'store_id' => $storeId,
                        'staff_id' => $staffId,
                        'role_col' => $role,
                        'start_at' => $shiftDate->copy()->setTime(14, 0),
                        'end_at' => $shiftDate->copy()->setTime(18, 0),
                        'note' => "Afternoon shift for {$role}",
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }

                // Evening shift (randomly assigned)
                if ($i % 3 === 0) {
                    $eveningRole = $roles[array_rand($roles)];
                    DB::table('staff_shifts')->insert([
                        'store_id' => $storeId,
                        'staff_id' => $staffId,
                        'role_col' => $eveningRole,
                        'start_at' => $shiftDate->copy()->setTime(18, 30),
                        'end_at' => $shiftDate->copy()->setTime(22, 0),
                        'note' => "Evening shift for {$eveningRole}",
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }
        }
    }
}