<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff_shifts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('store_id');
            $table->uuid('staff_id');
            $table->string('role_col')->comment('Using _col suffix to avoid reserved word conflicts');
            $table->timestamp('start_at');
            $table->timestamp('end_at');
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['store_id', 'staff_id']);
            $table->index(['start_at', 'end_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff_shifts');
    }
};
