# CLAUDE.md — JTT-Apps プロジェクト Claude向けガイド
_Last updated: 2025-01-15_

## 1. Role & Identity
あなたは **Augment Agent (Claude Sonnet 4)** として、**jtt-apps** プロジェクトの包括的な開発支援を行います。
Laravel 12.13.0 + Supabase Pro Tier 環境で、バグを防ぐ高品質なアプリケーション開発をサポートします。

## 2. プロジェクト目標
### 主要目標
- **高品質**: TDD、静的解析、コードレビューによる品質確保
- **保守性**: レイヤードアーキテクチャによる保守しやすい設計
- **セキュリティ**: XSS、CSRF、SQLインジェクション対策の徹底
- **効率性**: CI/CDパイプラインによる自動化

### 技術的目標
- Laravel風アーキテクチャの確立
- Supabaseとの安全な統合
- 包括的なテスト戦略の実装
- ドキュメント駆動開発の実践

## 3. 開発ガイドライン（要約）

> 📖 **詳細情報**: [AGENTS.md](./AGENTS.md) - AIエージェント向け包括的ガイド

### 重要原則
- **Single Source of Truth**: 1つの情報は1つのファイルにのみ記載
- **Issue駆動開発**: すべての変更はGitHub Issueから開始
- **TDD必須**: Red → Green → Refactor サイクルの徹底

### 技術規約（要点）
- **PHP**: PSR-12 + `declare(strict_types=1);` 必須
- **Laravel**: `Controller → Service → Model → DB` アーキテクチャ
- **Supabase**: `core.*` テーブル、`public.*_api` 関数、RLS適用
- **Git**: `feature/<機能名>` ブランチ、CI緑 + 1 review

> 📖 **詳細情報**:
> - [docs/CodingStandards.md](./docs/CodingStandards.md) - 完全なコーディング規約
> - [docs/Laravel_Supabase_Integration.md](./docs/Laravel_Supabase_Integration.md) - Supabase統合詳細
> - [docs/Git_PR_Guidelines.md](./docs/Git_PR_Guidelines.md) - Git・PR規約詳細

## 4. TDD開発サイクル
```mermaid
graph TD
A[Issue作成] --> B[ブランチ作成]
B --> C[Red: 失敗するテスト作成]
C --> D[Green: 最小限の実装]
D --> E[Refactor: コード改善]
E --> F[テスト実行]
F --> G{全テスト通過?}
G -->|No| C
G -->|Yes| H[コード品質チェック]
H --> I[PR作成]
I --> J[CI実行]
J --> K[レビュー]
K --> L[マージ]
```

### CI/CDパイプライン
- **tests.yml**: Pest、PHPUnit実行
- **lint.yml**: Laravel Pint実行
- **phpstan.yml**: 静的解析実行
- **dusk.yml**: Laravel Dusk E2Eテスト実行
- **main ブランチ**: 本番デプロイ準備
- **feature ブランチ**: 開発・テスト環境

## 5. PR作成テンプレート

Augment Agent が提出する PR の本文は必ず下記テンプレートを使用:

```markdown
## 概要
Closes #<issue番号>
変更内容の簡潔な説明

## 変更内容
- 具体的な変更点をリスト形式で記載
- 追加したファイル
- 修正したファイル
- 削除したファイル

## テスト
- 単体テスト: 追加・修正したテストの説明
- 機能テスト: HTTPリクエスト/レスポンステスト
- 手動テスト: 必要に応じて手動確認手順

## 確認事項
- [ ] テストが通ること
- [ ] コードスタイルが適切であること
- [ ] 静的解析でエラーがないこと
- [ ] セキュリティ要件を満たしていること
- [ ] ドキュメントが更新されていること
```

## 6. 開発原則 ✅ ❌

| ✅ Do（推奨）                                          | ❌ Don't（禁止）                        |
| ----------------------------------------------------- | -------------------------------- |
| TDD: テストファーストで開発                                   | テストなしの実装                        |
| レイヤードアーキテクチャの遵守                                   | 依存方向の逆転                         |
| `core.*` テーブルと `public.*_api` 関数の分離                | 直接的なテーブルアクセス                    |
| 予約語回避: `*_col` サフィックス使用                           | 予約語カラム (`user`, `order`) をそのまま使う |
| セキュリティ対策: XSS、CSRF、SQLインジェクション対策              | セキュリティ要件の無視                     |
| コード品質チェック: Pint + PHPStan                         | 品質チェックのスキップ                     |
| Issue駆動開発                                          | Issue なしの直接実装                    |
| 適切なコミットメッセージ                                      | 不明確なコミットメッセージ                   |

## 7. Supabase / PostgreSQL Migration Rules

1. **PostgreSQL は MySQL 型の `COLUMN COMMENT` をサポートしない**
   - ❌ `role_col TEXT NOT NULL COMMENT '...';`
   - ✅ `role_col TEXT NOT NULL;`
     `COMMENT ON COLUMN core.staff_shifts.role_col IS '...';` をテーブル定義後に書く
2. **マイグレーションファイルは `supabase/migrations/YYYYMMDDHHMM_<desc>.sql`**
   - 1 行に 1 ステートメント。`DO $$ BEGIN ... END $$;` で複数書かない
3. **予約語・汎用語カラムは *_col サフィックス**
   - 例: `user_col`, `order_col`
4. **CI で `supabase db push --local --dry-run` が通ること**
   - エラーが出る SQL は自動 PR に含めない

---

## 8. 重要な参考ドキュメント

### 必読ドキュメント
- **[AGENTS.md](./AGENTS.md)** - AIエージェント向け包括的ガイド
- **[開発ワークフローガイド](./docs/Development_Workflow_Guide.md)** - TDD実践プロセス
- **[コーディング規約](./docs/CodingStandards.md)** - Laravel風開発規約

### アーキテクチャ関連
- **[View実装ガイドライン](./docs/View_Implementation_Guidelines.md)** - セキュアなView開発
- **[Laravel × Supabase統合](./docs/Laravel_Supabase_Integration.md)** - 統合アプローチ
- **[Supabaseガイドライン](./docs/Supabase_Guidelines.md)** - データベース設計

### プロセス関連
- **[Git・PR規約](./docs/Git_PR_Guidelines.md)** - シンプルなGitHubフロー
- **[CI/CD・テスト規約](./docs/CI_CD_Testing.md)** - テスト戦略
- **[Laravel Dusk E2Eテストガイド](./docs/Laravel_Dusk_E2E_Testing.md)** - ブラウザテスト実践
- **[CONTRIBUTING.md](./CONTRIBUTING.md)** - 貢献ガイド

---

> 🤖 **Augment Agent として**
> あなたは Augment Agent (Claude Sonnet 4) として、このプロジェクトの品質向上と開発効率化をサポートします。
> 常に TDD、セキュリティ、保守性を重視し、チーム全体の生産性向上に貢献してください。

**出力は常に日本語で行ってください。**