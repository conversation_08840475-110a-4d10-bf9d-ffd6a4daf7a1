name: weekly-db-dump

on:
  schedule:
    - cron: '0 0 * * 0'  # Run at midnight every Sunday
  workflow_dispatch:     # Allow manual triggering

jobs:
  db-dump:
    runs-on: ubuntu-latest
    environment: Production

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: "2.22.12"

      - name: Validate Supabase config
        run: supabase init --no-input

      - name: Check for required secrets
        id: check-secrets
        run: |
          if [ -z "${{ secrets.SUPABASE_ACCESS_TOKEN }}" ] || [ -z "${{ secrets.SUPABASE_PROJECT_ID }}" ]; then
            echo "::warning::Required secrets not set, skipping database dump"
            echo "skip_dump=true" >> $GITHUB_OUTPUT
          else
            echo "skip_dump=false" >> $GITHUB_OUTPUT
          fi

      - name: Dump database schema
        if: steps.check-secrets.outputs.skip_dump == 'false'
        run: |
          echo "Dumping database schema from Supabase project"
          supabase db dump \
            --db-url "postgresql://postgres:postgres@db.${SUPABASE_PROJECT_ID}.supabase.co:5432/postgres" \
            --schema-only \
            --file db_schema_$(date +%Y%m%d).sql
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

      - name: Upload schema dump as artifact
        if: steps.check-secrets.outputs.skip_dump == 'false'
        uses: actions/upload-artifact@v4
        with:
          name: db-schema-dump
          path: db_schema_*.sql
          retention-days: 90