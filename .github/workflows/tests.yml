name: tests

on:
  push:
    branches:
      - develop
      - main
  pull_request:
    branches:
      - develop
      - main

jobs:
  ci:
    runs-on: ubuntu-latest
    environment: Testing

    services:
      supabase:
        image: supabase/supabase-local:latest
        ports:
          - 54321:54321
          - 54322:54322

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: xdebug

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Node Dependencies
        run: npm i

      - name: Install Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader --ignore-platform-reqs --no-plugins

      - name: Copy Environment File
        run: cp .env.example .env

      - name: Generate Application Key
        run: php artisan key:generate

      - name: Build Assets
        run: npm run build

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: "2.22.12"

      - name: Run Supabase DB Push (local dry-run)
        run: supabase db push --local --dry-run
        continue-on-error: false

      - name: Run Database Migrations and Seeding
        run: php artisan migrate --seed
        continue-on-error: false

      - name: Run Tests
        run: ./vendor/bin/pest
