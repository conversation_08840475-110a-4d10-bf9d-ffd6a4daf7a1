# Project Guidelines for <PERSON>ie (jtt-apps)

*Last updated: 2025‑05‑14*

> **<PERSON>ie の出力はすべて日本語で記述すること。**

---

## 0. <PERSON> Stack (locked)

| Layer        | Version / Choice                                        | Notes                                                  |
| ------------ | ------------------------------------------------------- | ------------------------------------------------------ |
| PHP          | **8.3.21**                                              | `.tool-versions` fixed                                 |
| Node         | **22.15.0**                                             | same                                                   |
| Laravel      | **12.13**                                               | Framework core                                         |
| Database     | **Supabase Postgres**                                   | *Single source of truth* for menus, reservations, etc. |
| ORM          | **Eloquent Active‑Record**                              | Default for CRUD. See §1.                              |
| Front‑end    | **React + Inertia** (v0 code) *or* **Livewire islands** | Decide via §6 matrix                                   |
| Admin UI     | **Filament 3** (install only)                           | No v4 migration yet                                    |
| External API | Smaregi via **Saloon Connectors**                       | Token cached 55 min                                    |

---

## 1. Coding Rules

1. **CRUD = Eloquent.** No raw SQL in Controllers/Views.
2. Use **Query Scopes** for reusable conditions.
3. Controllers ≤ 25 LOC; move business logic to `app/Actions/…`.
4. Create **FormRequest** for validation, **Resource** for JSON.
5. Run tests & PHPStan before finishing a task.

```php
// Example scope
public function scopeAvailable($q)
{
    return $q->where('is_available', true);
}
```

---

## 2. Repo Structure (excerpt)

```
app/
  Actions/           # Service classes
  Models/
  Filament/         # Admin Resources (v3)
resources/
  js/Pages/         # React Inertia pages
  views/            # Blade layouts & Livewire islands
supabase/
  migrations/
```

---

## 3. Build & Test Workflow (Junie must follow)

```bash
supabase start         # containers
asdf install              # PHP & Node
composer install
npm ci
php artisan migrate --seed
php artisan test --parallel
npm run test:e2e          # Playwright
./vendor/bin/phpstan analyse
npm run build             # Vite
```

*Tests require the Supabase containers. Always start them first.*

---

## 4. Code‑Style

* **PHP**: PSR‑12 + Laravel Pint.
* **JS/TS**: Prettier 2‑space.
* Enum casts for status fields.
* Commit prefixes: `feat:`, `fix:`, `chore:`, `docs:`, `refactor:`.

---

## 5. CSV Export Standard

* **Delimiter:** `;`
* **Encoding:** UTF‑8 BOM
* **Headings order:** `Category;Item;Description;Price;InStock`
  Implemented via Filament `ExportAction`.

---

## 6. React vs Livewire Decision Matrix

| Need                                  | Pick                |
| ------------------------------------- | ------------------- |
| Full SPA navigation / complex state   | **React + Inertia** |
| Static page with small dynamic widget | **Livewire island** |
| Must work w/o JS (SEO landing)        | Blade only          |

---

## 7. Smaregi Connector Contract

* Use `client_credentials` flow.
* Classes live in `app/Integrations/Smaregi/`.
* Throw `SmaregiRequestException` on 4xx/5xx.

---

## 8. Filament Guardrails

* **Install v3 only**, no v4 migration unless asked.
* Resources in `app/Filament/Resources`.
* Use **Tiptap** for rich‑text, **ExportAction** for CSV.

---

## 9. Forbidden

* Service‑Role Supabase key in client JS.
* Doctrine ORM.
* Direct DB calls from React.
* Pushing to `main` – use PR to `develop` with green CI.

---

💡 **Tip for Junie:** Add sub‑tasks like `run tests` or `lint code` in your plan. Ask in chat if any step is unclear.

---

## 10. Ignored paths (Junie は無視)

* `docs/my_laravel_html_manual/**`  # private HTML manual — do not load or parse
