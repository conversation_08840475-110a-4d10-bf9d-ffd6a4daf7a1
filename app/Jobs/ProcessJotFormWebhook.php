<?php

declare(strict_types=1);

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class ProcessJotFormWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * The webhook data from JotForm.
     *
     * @var array<string, mixed>
     */
    protected array $webhookData;

    /**
     * Create a new job instance.
     *
     * @param array<string, mixed> $webhookData The webhook payload from JotForm
     */
    public function __construct(array $webhookData)
    {
        $this->webhookData = $webhookData;
    }

    /**
     * Execute the job to process JotForm webhook and determine staff assignment.
     *
     * @return void
     *
     * @throws \Exception If processing fails after retries
     */
    public function handle(): void
    {
        try {
            Log::info('Processing JotForm webhook', ['form_id' => $this->webhookData['formId'] ?? 'unknown']);

            // Extract required information from webhook data
            $staffId = $this->extractStaffId();
            $formData = $this->extractFormData();

            if (!$staffId) {
                Log::error('Staff ID not found in webhook data');
                return;
            }

            // Get today's shift using Supabase get_today_shift_api function
            $shifts = $this->getTodayShifts($staffId);

            if (empty($shifts)) {
                Log::warning('No shifts found for staff', ['staff_id' => $staffId]);
                // Handle case where no shifts are found
                $this->handleNoShiftsFound($staffId, $formData);
                return;
            }

            // Determine staff assignment based on shifts
            $assignedStaff = $this->determineAssignment($shifts);

            // Process the form data with the assigned staff
            $this->processWithAssignedStaff($formData, $assignedStaff);

            Log::info('JotForm webhook processed successfully', [
                'staff_id' => $staffId,
                'assigned_to' => $assignedStaff['staff_id'] ?? null,
            ]);
        } catch (Throwable $e) {
            Log::error('Error processing JotForm webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'attempt' => $this->attempts(),
            ]);

            // Re-throw for job retry
            throw $e;
        }
    }

    /**
     * Extract staff ID from webhook data.
     *
     * @return string|null The UUID of the staff member
     */
    protected function extractStaffId(): ?string
    {
        // Implementation depends on the structure of JotForm data
        return $this->webhookData['staffId'] ?? null;
    }

    /**
     * Extract form submission data from webhook.
     *
     * @return array<string, mixed> The form submission data
     */
    protected function extractFormData(): array
    {
        return $this->webhookData['formData'] ?? [];
    }

    /**
     * Get today's shifts using the Supabase get_today_shift_api function.
     *
     * @param string $staffId The staff UUID to query shifts for
     *
     * @return array<int, array<string, mixed>> List of shifts for today
     */
    protected function getTodayShifts(string $staffId): array
    {
        $supabaseUrl = config('services.supabase.url');
        $supabaseKey = config('services.supabase.key');

        if (!$supabaseUrl || !$supabaseKey) {
            Log::error('Supabase configuration is missing');
            return [];
        }

        $response = Http::withHeaders([
            'apikey' => $supabaseKey,
            'Authorization' => 'Bearer ' . $supabaseKey,
            'Content-Type' => 'application/json',
            'Prefer' => 'return=representation',
        ])->post("{$supabaseUrl}/rest/v1/rpc/get_today_shift_api", [
            'p_staff_id' => $staffId,
        ]);

        if ($response->failed()) {
            Log::error('Failed to fetch today\'s shifts', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            return [];
        }

        return $response->json() ?? [];
    }

    /**
     * Determine staff assignment based on shift data.
     *
     * @param array<int, array<string, mixed>> $shifts The shifts data from Supabase
     *
     * @return array<string, mixed> The selected staff assignment
     */
    protected function determineAssignment(array $shifts): array
    {
        // Default to the first shift if multiple exist
        $selectedShift = $shifts[0];

        // Find a shift that's currently active
        $now = Carbon::now();
        foreach ($shifts as $shift) {
            $startAt = Carbon::parse($shift['start_at']);
            $endAt = Carbon::parse($shift['end_at']);

            if ($now->between($startAt, $endAt)) {
                $selectedShift = $shift;
                break;
            }
        }

        return $selectedShift;
    }

    /**
     * Handle case where no shifts are found for the staff.
     *
     * @param string $staffId The staff UUID
     * @param array<string, mixed> $formData The form data
     *
     * @return void
     */
    protected function handleNoShiftsFound(string $staffId, array $formData): void
    {
        // Implementation depends on business requirements
        Log::info('No shifts found, using fallback assignment logic', [
            'staff_id' => $staffId,
        ]);

        // Potential fallback logic could be implemented here
    }

    /**
     * Process the form with the assigned staff member.
     *
     * @param array<string, mixed> $formData The form submission data
     * @param array<string, mixed> $assignedStaff The assigned staff data
     *
     * @return void
     */
    protected function processWithAssignedStaff(array $formData, array $assignedStaff): void
    {
        // Implementation depends on business requirements
        Log::info('Processing form with assigned staff', [
            'role' => $assignedStaff['role_col'] ?? 'unknown',
            'shift_start' => $assignedStaff['start_at'] ?? null,
            'shift_end' => $assignedStaff['end_at'] ?? null,
        ]);

        // Business logic for form processing goes here
    }
}
