<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StaffShift extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'staff_shifts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'store_id',
        'staff_id',
        'role_col',
        'start_at',
        'end_at',
        'note',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the staff member that owns the shift.
     */
    public function staff()
    {
        return $this->belongsTo(User::class, 'staff_id', 'id');
    }

    /**
     * Scope a query to only include active shifts for the current day.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTodayShifts($query, $staffId = null)
    {
        $query = $query->whereDate('start_at', Carbon::today());
        
        if ($staffId) {
            $query->where('staff_id', $staffId);
        }
        
        return $query->orderBy('start_at', 'asc');
    }

    /**
     * Determine if shift is currently active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        $now = Carbon::now();
        return $now->between($this->start_at, $this->end_at);
    }

    /**
     * Get formatted duration of the shift.
     *
     * @return string
     */
    public function getDurationAttribute(): string
    {
        $duration = $this->start_at->diffInHours($this->end_at);
        return "{$duration} hour" . ($duration != 1 ? 's' : '');
    }
}