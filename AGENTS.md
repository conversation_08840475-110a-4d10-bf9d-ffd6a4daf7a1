# JTT-Apps プロジェクト AIエージェント向け標準ガイド

> **目的**: すべての **AI エージェントと新規開発者** が "迷わず・安全に・バグなく" **Laravel風アーキテクチャ** で実装できる唯一の開発リファレンス

## 目次

### 🚀 新規参画者向け
0. **[クイックスタートガイド](./docs/Quick_Start_Guide.md)** - 5分で開発開始
1. **[ドキュメント構造ガイド](./docs/Documentation_Structure_Guide.md)** - 参照型ファイル構造の理解

### 📋 開発プロセス
2. [開発ワークフローガイド](./docs/Development_Workflow_Guide.md) - TDD実践とバグ防止プロセス
3. [TDDガイド](./docs/TDD_Guide.md) - テスト駆動開発の実践
4. [Git・PR規約](./docs/Git_PR_Guidelines.md) - シンプルなGitHubフロー
5. [CI/CD・テスト規約](./docs/CI_CD_Testing.md) - TDD実践とテスト戦略

### 🔧 技術仕様
6. [コーディング規約](./docs/CodingStandards.md) - Laravel風の開発規約
7. [Laravel Artisanコマンドガイド](./docs/Laravel_Artisan_Commands_Guide.md) - 必須ツールによる統一開発
8. [View実装ガイドライン](./docs/View_Implementation_Guidelines.md) - セキュアなView開発
9. [Laravel Dusk E2Eテストガイド](./docs/Laravel_Dusk_E2E_Testing.md) - ブラウザテスト実践
10. [セキュリティガイドライン](./docs/Security_Guidelines.md) - セキュリティ対策

### 🗄 インフラ・DB
11. [Laravel × Supabase統合](./docs/Laravel_Supabase_Integration.md) - 統合アプローチ
12. [Supabaseガイドライン](./docs/Supabase_Guidelines.md) - データベース設計
13. [マイグレーション規約](./docs/Laravel_Migration_Convention.md) - スキーマ管理

### 📊 プロジェクト管理
14. [プロジェクト管理ガイド](./docs/Project_Management_Guide.md) - 効率的なプロジェクト管理

## プロジェクト概要

> 📖 **詳細情報**: [README.md](./README.md) - プロジェクト概要とクイックスタート

### 🎯 プロジェクト目標
高品質で保守性の高いWebアプリケーションを、TDD、適切なGitフロー、CI/CDパイプラインを通じて開発する。

### 🛠 技術スタック・環境要件
- **Backend**: Laravel 12.13.0 (PHP 8.3.6)
- **Frontend**: Livewire + Flux UI / Inertia.js + Vue
- **Database**: Supabase (PostgreSQL)
- **Testing**: Pest + Laravel Dusk
- **CI/CD**: GitHub Actions

> 📖 **詳細情報**: [README.md](./README.md#技術スタック) - 完全な技術スタックと環境構築手順

## 重要原則

> 📖 **詳細情報**: [docs/Documentation_Structure_Guide.md](./docs/Documentation_Structure_Guide.md) - 参照型ファイル構造の原則

### プロジェクト管理原則
- **🎯 GitHub Issues管理**: このプロジェクトは全てGitHub IssuesでTODOを管理
- **Issue駆動開発**: すべての変更はIssueから開始
- **Single Source of Truth**: 1つの情報は1つのファイルにのみ記載

> 📖 **詳細情報**: [docs/Project_Management_Guide.md](./docs/Project_Management_Guide.md) - プロジェクト管理の詳細

### アーキテクチャ・セキュリティ原則
- **レイヤードアーキテクチャ**: `Controller → Service → Model → DB`
- **テストファースト**: TDDによる品質確保
- **セキュリティファースト**: XSS、CSRF、SQLインジェクション対策

> 📖 **詳細情報**:
> - [docs/CodingStandards.md](./docs/CodingStandards.md) - アーキテクチャとコーディング規約
> - [docs/Security_Guidelines.md](./docs/Security_Guidelines.md) - セキュリティ対策詳細

### データベース原則
- **Supabase統合**: Laravel + Supabase Migration同期
- **RLS適用**: Row Level Securityによるデータ保護
- **予約語回避**: `*_col` サフィックス使用

> 📖 **詳細情報**: [docs/Laravel_Supabase_Integration.md](./docs/Laravel_Supabase_Integration.md) - Supabase統合ガイド

## AIエージェント役割分担

| AIエージェント | 主な役割 | 責任範囲 |
|-----------|--------|--------|
| **Claude (Augment Agent)** | アーキテクチャ設計、コードレビュー、品質保証 | 設計文書の保守、レビュー基準適用、品質チェック |
| **GitHub Copilot** | コード補完、実装支援 | 開発効率向上、コーディング支援 |
| **その他AIツール** | 特定タスク支援 | ドキュメント生成、テスト作成支援 |

## 開発フロー

> 📖 **詳細情報**: [docs/Development_Workflow_Guide.md](./docs/Development_Workflow_Guide.md) - 完全な開発ワークフローガイド

### 基本ワークフロー（要約）
1. **Issue作成** → **ブランチ作成** → **TDD実践** → **PR作成** → **レビュー** → **マージ**

### TDD（テスト駆動開発）サイクル
1. **Red**: 失敗するテストを先に書く
2. **Green**: テストを通す最小限の実装
3. **Refactor**: テストが通る状態でコードを改善

> 📖 **詳細情報**: [docs/TDD_Guide.md](./docs/TDD_Guide.md) - TDD実践の詳細手順

### 必須チェック項目
- [ ] テストが通ること（`php artisan test` + `php artisan dusk`）
- [ ] コードスタイルが適切であること（Laravel Pint）
- [ ] 静的解析でエラーがないこと（PHPStan）
- [ ] セキュリティ要件を満たしていること

> 📖 **詳細情報**: [docs/CI_CD_Testing.md](./docs/CI_CD_Testing.md) - CI/CD・テスト戦略

## コーディング規約（要約）

> 📖 **詳細情報**: [docs/CodingStandards.md](./docs/CodingStandards.md) - 完全なコーディング規約

### PHP・Laravel規約（要点）
- **スタイル**: PSR-12準拠、`declare(strict_types=1);` 必須
- **命名**: クラス PascalCase、メソッド camelCase、定数 SNAKE_CASE_CAPS
- **Migration**: `YYYYMMDDHHMM_<desc>.php` 形式
- **予約語回避**: `*_col` サフィックス使用

### Supabase規約（要点）
- **スキーマ分離**: `core.*` テーブル、`public.*_api` 関数
- **RLS**: Row Level Security適用
- **マイグレーション**: `supabase/migrations/` 配置

> 📖 **詳細情報**: [docs/Laravel_Supabase_Integration.md](./docs/Laravel_Supabase_Integration.md) - Supabase統合詳細

## テスト戦略

> 📖 **詳細情報**: [docs/CI_CD_Testing.md](./docs/CI_CD_Testing.md) - 完全なテスト戦略

### テストピラミッド（要約）
- **単体テスト（70%）**: Service層の完全カバー
- **統合テスト（20%）**: 複数コンポーネントの連携
- **E2Eテスト（10%）**: Laravel Duskによるブラウザテスト

### 主要テスト実行コマンド
```bash
# 全テスト実行
php artisan test

# E2Eテスト実行
php artisan dusk
```

> 📖 **詳細情報**:
> - [docs/TDD_Guide.md](./docs/TDD_Guide.md) - TDD実践方法
> - [docs/Laravel_Dusk_E2E_Testing.md](./docs/Laravel_Dusk_E2E_Testing.md) - E2Eテスト詳細

## CI/CD パイプライン

> 📖 **詳細情報**: [docs/CI_CD_Testing.md](./docs/CI_CD_Testing.md) - CI/CD・テスト戦略

### GitHub Actions ワークフロー（要約）
- **tests.yml**: Pest、PHPUnit実行
- **lint.yml**: Laravel Pint実行
- **phpstan.yml**: 静的解析実行
- **dusk.yml**: Laravel Dusk E2Eテスト実行

## PR管理

> 📖 **詳細情報**: [docs/Git_PR_Guidelines.md](./docs/Git_PR_Guidelines.md) - Git・PR規約詳細

### PR作成ルール（要約）
- **命名**: `type(scope): 説明`
- **レビュー**: 最低1人の承認が必要
- **CI**: 全チェックが緑になること

> 📖 **詳細情報**: [CONTRIBUTING.md](./CONTRIBUTING.md) - 貢献ガイド

## セキュリティガイドライン

> 📖 **詳細情報**: [docs/Security_Guidelines.md](./docs/Security_Guidelines.md) - 包括的なセキュリティ対策

### 必須セキュリティ対策（要約）
1. **入力検証**: FormRequestによる厳密なバリデーション
2. **出力エスケープ**: Bladeテンプレートでの適切なエスケープ
3. **CSRF保護**: 全フォームでトークン検証
4. **認証・認可**: Policyによる適切な権限管理
5. **SQLインジェクション対策**: Eloquent ORM使用

## 運用ルール

> 📖 **詳細情報**: [docs/Documentation_Structure_Guide.md](./docs/Documentation_Structure_Guide.md) - ドキュメント構造と更新方法

### ドキュメント管理（要約）
- **Single Source of Truth**: 1つの情報は1つのファイルにのみ記載
- **参照型構造**: 詳細は適切なマスターファイルに誘導
- **変更管理**: Issue作成→PR→マージのフローを遵守

### 品質確保（要約）
- **コードレビュー**: チェックリストに沿って確認
- **テスト**: TDDサイクルの遵守
- **アーキテクチャ**: 依存方向の厳守

## AIエージェント連携方法

### 情報共有
- 重要な変更は他のAIエージェントに明示的に通知
- 関連するドキュメントへの参照を常に含める
- 不明確な点は「【不明確】」と明記し、推測は避ける

### タスク引き継ぎ
1. 現在の進捗状況
2. 実装済みの機能と残タスク
3. 直面している課題やブロッカー
4. 関連する Issue や PR の番号
5. 参照すべきドキュメントやコード

> 📖 **詳細情報**: [docs/Project_Management_Guide.md](./docs/Project_Management_Guide.md) - プロジェクト管理詳細

---

> **重要**: このガイドラインに従うことで、チーム全体でバグの少ない高品質なLaravelアプリケーションを開発できます。不明な点があれば、必ずドキュメントを参照し、推測での実装は避けてください。
