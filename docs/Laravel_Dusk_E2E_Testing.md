# Laravel Dusk E2Eテストガイド
## ブラウザテストによる包括的な品質保証

### 概要
Laravel DuskはLaravelに完全統合されたブラウザテストツールです。実際のブラウザを使用してユーザー視点でのテストを実行し、JavaScriptの動作も含めた包括的なテストが可能です。

## Laravel Duskの利点

### Playwrightとの比較
| 項目 | Laravel Dusk | Playwright |
|------|-------------|------------|
| **Laravel統合** | ✅ 完全統合 | ❌ 外部ツール |
| **設定の簡単さ** | ✅ 最小限の設定 | ❌ 複雑な設定 |
| **Eloquentアクセス** | ✅ 直接アクセス可能 | ❌ API経由のみ |
| **認証テスト** | ✅ Laravel認証と連携 | ❌ 手動実装 |
| **データベース操作** | ✅ Migration/Seeder使用 | ❌ 外部セットアップ |
| **学習コスト** | ✅ Laravel開発者に馴染み深い | ❌ 新しいAPI学習 |

### Laravel Duskの特徴
- **Laravel生態系**: Eloquent、Migration、Seederとの完全統合
- **認証連携**: Laravel認証システムとの自然な連携
- **データベース管理**: テスト用データベースの自動管理
- **日本語対応**: 日本語UIのテストも問題なし

## セットアップ

### インストール
```bash
# Laravel Duskのインストール
composer require --dev laravel/dusk

# Duskのセットアップ
php artisan dusk:install

# ChromeDriverのインストール
php artisan dusk:chrome-driver
```

### 環境設定
```bash
# .env.dusk.local ファイル作成
cp .env .env.dusk.local
```

```env
# .env.dusk.local
APP_URL=http://localhost:8000
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Dusk専用設定
DUSK_DRIVER_URL=http://localhost:9515
```

### 基本設定
```php
<?php
// tests/DuskTestCase.php

namespace Tests;

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Laravel\Dusk\TestCase as BaseTestCase;

abstract class DuskTestCase extends BaseTestCase
{
    use CreatesApplication;

    /**
     * Prepare for Dusk test execution.
     */
    public static function prepare(): void
    {
        if (! static::runningInSail()) {
            static::startChromeDriver();
        }
    }

    /**
     * Create the RemoteWebDriver instance.
     */
    protected function driver(): RemoteWebDriver
    {
        $options = (new ChromeOptions)->addArguments(collect([
            $this->shouldStartMaximized() ? '--start-maximized' : '--window-size=1920,1080',
            '--disable-search-engine-choice-screen',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--headless', // ヘッドレスモード（CI用）
        ])->unless($this->hasHeadlessDisabled(), function ($items) {
            return $items->forget(collect($items)->search('--headless'));
        })->all());

        return RemoteWebDriver::create(
            $_ENV['DUSK_DRIVER_URL'] ?? 'http://localhost:9515',
            DesiredCapabilities::chrome()->setCapability(
                ChromeOptions::CAPABILITY, $options
            )
        );
    }
}
```

## 実践例：スタッフシフト管理のE2Eテスト

### 基本的なページテスト
```php
<?php
// tests/Browser/StaffShiftTest.php

namespace Tests\Browser;

use App\Models\User;
use App\Models\StaffShift;
use Laravel\Dusk\Browser;
use Tests\DuskTestCase;

class StaffShiftTest extends DuskTestCase
{
    use DatabaseMigrations;

    public function test_user_can_view_shift_list(): void
    {
        $user = User::factory()->create();
        $shifts = StaffShift::factory()->count(3)->create(['staff_id' => $user->id]);

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/staff-shifts')
                    ->assertSee('シフト一覧')
                    ->assertSeeIn('@shift-list', $user->name);
        });
    }

    public function test_user_can_create_new_shift(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/staff-shifts')
                    ->clickLink('新規作成')
                    ->assertPathIs('/staff-shifts/create')
                    ->select('staff_id', $user->id)
                    ->type('start_time', '2025-01-01T09:00')
                    ->type('end_time', '2025-01-01T17:00')
                    ->select('role_col', 'cashier')
                    ->press('作成')
                    ->assertPathIs('/staff-shifts')
                    ->assertSee('シフトが作成されました');

            // データベースに保存されていることを確認
            $this->assertDatabaseHas('staff_shifts', [
                'staff_id' => $user->id,
                'role_col' => 'cashier',
            ]);
        });
    }

    public function test_user_cannot_create_overlapping_shift(): void
    {
        $user = User::factory()->create();
        
        // 既存シフト作成
        StaffShift::factory()->create([
            'staff_id' => $user->id,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/staff-shifts/create')
                    ->select('staff_id', $user->id)
                    ->type('start_time', '2025-01-01T10:00')
                    ->type('end_time', '2025-01-01T16:00')
                    ->select('role_col', 'cashier')
                    ->press('作成')
                    ->assertSee('シフトが重複しています');
        });
    }
}
```

### Livewireコンポーネントのテスト
```php
<?php
// tests/Browser/LivewireShiftManagerTest.php

namespace Tests\Browser;

use App\Models\User;
use App\Models\StaffShift;
use Laravel\Dusk\Browser;
use Tests\DuskTestCase;

class LivewireShiftManagerTest extends DuskTestCase
{
    use DatabaseMigrations;

    public function test_real_time_shift_updates(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/shift-manager')
                    ->assertSee('シフト管理')
                    ->click('@create-shift-button')
                    ->whenAvailable('@shift-modal', function ($modal) use ($user) {
                        $modal->select('staff_id', $user->id)
                              ->type('start_time', '2025-01-01T09:00')
                              ->type('end_time', '2025-01-01T17:00')
                              ->select('role_col', 'cashier')
                              ->press('作成');
                    })
                    ->waitForText('シフトが作成されました')
                    ->assertDontSee('@shift-modal') // モーダルが閉じる
                    ->assertSeeIn('@shift-list', $user->name); // リストが更新される
        });
    }

    public function test_shift_filtering_works(): void
    {
        $user1 = User::factory()->create(['name' => '田中太郎']);
        $user2 = User::factory()->create(['name' => '佐藤花子']);
        
        StaffShift::factory()->create(['staff_id' => $user1->id, 'role_col' => 'cashier']);
        StaffShift::factory()->create(['staff_id' => $user2->id, 'role_col' => 'kitchen']);

        $this->browse(function (Browser $browser) use ($user1, $user2) {
            $browser->loginAs($user1)
                    ->visit('/shift-manager')
                    ->assertSee('田中太郎')
                    ->assertSee('佐藤花子')
                    ->select('@role-filter', 'cashier')
                    ->waitForText('田中太郎')
                    ->assertSee('田中太郎')
                    ->assertDontSee('佐藤花子');
        });
    }
}
```

### フォームバリデーションのテスト
```php
<?php
// tests/Browser/FormValidationTest.php

namespace Tests\Browser;

use App\Models\User;
use Laravel\Dusk\Browser;
use Tests\DuskTestCase;

class FormValidationTest extends DuskTestCase
{
    use DatabaseMigrations;

    public function test_form_validation_displays_errors(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/staff-shifts/create')
                    ->press('作成') // 空のフォームで送信
                    ->assertSee('スタッフを選択してください')
                    ->assertSee('開始時間を入力してください')
                    ->assertSee('終了時間を入力してください')
                    ->assertSee('役割を選択してください');
        });
    }

    public function test_real_time_validation_with_precognition(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/staff-shifts/create')
                    ->type('start_time', '2025-01-01T17:00')
                    ->type('end_time', '2025-01-01T09:00') // 終了時間が開始時間より前
                    ->waitForText('終了時間は開始時間より後である必要があります')
                    ->assertSee('終了時間は開始時間より後である必要があります');
        });
    }
}
```

## ページオブジェクトパターン

### ページクラスの作成
```php
<?php
// tests/Browser/Pages/StaffShiftCreatePage.php

namespace Tests\Browser\Pages;

use Laravel\Dusk\Browser;
use Laravel\Dusk\Page;

class StaffShiftCreatePage extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return '/staff-shifts/create';
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url())
                ->assertSee('シフト作成');
    }

    /**
     * Get the element shortcuts for the page.
     */
    public function elements(): array
    {
        return [
            '@staff-select' => 'select[name="staff_id"]',
            '@start-time' => 'input[name="start_time"]',
            '@end-time' => 'input[name="end_time"]',
            '@role-select' => 'select[name="role_col"]',
            '@submit-button' => 'button[type="submit"]',
            '@cancel-button' => 'button[onclick="history.back()"]',
        ];
    }

    /**
     * Fill the shift form.
     */
    public function fillShiftForm(Browser $browser, array $data): void
    {
        $browser->select('@staff-select', $data['staff_id'])
                ->type('@start-time', $data['start_time'])
                ->type('@end-time', $data['end_time'])
                ->select('@role-select', $data['role_col']);
    }

    /**
     * Submit the form.
     */
    public function submitForm(Browser $browser): void
    {
        $browser->click('@submit-button');
    }
}
```

### ページオブジェクトの使用
```php
<?php
// tests/Browser/StaffShiftPageObjectTest.php

namespace Tests\Browser;

use App\Models\User;
use Laravel\Dusk\Browser;
use Tests\Browser\Pages\StaffShiftCreatePage;
use Tests\DuskTestCase;

class StaffShiftPageObjectTest extends DuskTestCase
{
    use DatabaseMigrations;

    public function test_create_shift_using_page_object(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit(new StaffShiftCreatePage)
                    ->fillShiftForm([
                        'staff_id' => $user->id,
                        'start_time' => '2025-01-01T09:00',
                        'end_time' => '2025-01-01T17:00',
                        'role_col' => 'cashier',
                    ])
                    ->submitForm()
                    ->assertPathIs('/staff-shifts')
                    ->assertSee('シフトが作成されました');
        });
    }
}
```

## CI/CD統合

### GitHub Actions設定
```yaml
# .github/workflows/dusk.yml
name: Laravel Dusk

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  dusk-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: mbstring, dom, fileinfo, pdo, sqlite
        
    - name: Install Supabase CLI
      run: |
        curl -fsSL https://github.com/supabase/cli/releases/download/v2.22.12/supabase_2.22.12_linux_amd64.deb -o supabase.deb
        sudo dpkg -i supabase.deb
        
    - name: Copy .env
      run: cp .env.dusk.ci .env.dusk.local
      
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress
      
    - name: Generate key
      run: php artisan key:generate
      
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
      
    - name: Create Database
      run: touch database/database.sqlite
      
    - name: Run Migrations
      run: php artisan migrate --env=dusk.local
      
    - name: Install Chrome Driver
      run: php artisan dusk:chrome-driver
      
    - name: Start Chrome Driver
      run: ./vendor/laravel/dusk/bin/chromedriver-linux &
      
    - name: Run Laravel Server
      run: php artisan serve --env=dusk.local &
      
    - name: Run Dusk Tests
      run: php artisan dusk
      
    - name: Upload Screenshots
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: screenshots
        path: tests/Browser/screenshots
        
    - name: Upload Console Logs
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: console-logs
        path: tests/Browser/console
```

## ベストプラクティス

### 1. テストの独立性
```php
public function test_example(): void
{
    // 各テストでデータベースをリセット
    $this->artisan('migrate:fresh');
    
    // 必要なデータのみ作成
    $user = User::factory()->create();
    
    // テスト実行
    $this->browse(function (Browser $browser) use ($user) {
        // テスト内容
    });
}
```

### 2. 待機処理の適切な使用
```php
// ✅ 良い例：特定の要素を待つ
$browser->waitForText('読み込み完了')
        ->waitFor('@dynamic-content')
        ->waitUntilMissing('@loading-spinner');

// ❌ 悪い例：固定時間の待機
$browser->pause(3000); // 避けるべき
```

### 3. エラーハンドリング
```php
public function test_with_error_handling(): void
{
    $this->browse(function (Browser $browser) {
        try {
            $browser->visit('/staff-shifts')
                    ->assertSee('シフト一覧');
        } catch (Exception $e) {
            // スクリーンショットを保存
            $browser->screenshot('error-' . now()->timestamp);
            throw $e;
        }
    });
}
```

## テスト実行

### ローカル実行
```bash
# 全Duskテスト実行
php artisan dusk

# 特定テスト実行
php artisan dusk tests/Browser/StaffShiftTest.php

# ヘッドレスモード無効（ブラウザ表示）
php artisan dusk --without-headless

# 並列実行
php artisan dusk --parallel
```

### デバッグ
```bash
# スクリーンショット確認
ls tests/Browser/screenshots/

# コンソールログ確認
ls tests/Browser/console/

# 詳細ログ出力
php artisan dusk --verbose
```

## まとめ

Laravel Duskにより：
1. **Laravel統合**: 自然なテスト環境
2. **包括的テスト**: JavaScript含む完全なE2Eテスト
3. **保守性**: ページオブジェクトパターンによる構造化
4. **CI/CD統合**: 自動化されたブラウザテスト

PlaywrightよりもLaravel開発者にとって学習コストが低く、より効率的なE2Eテストが実現できます。
