# View実装ガイドライン
## Laravel風のView開発でバグを防ぐ

### 基本原則

#### 1. Bladeテンプレートの活用
- Laravel標準のBladeテンプレートエンジンを使用
- コンポーネント指向の設計を採用
- 再利用可能なパーツを作成

#### 2. セキュリティファースト
- XSS攻撃を防ぐため、常にエスケープ処理を実施
- CSRF保護を適切に実装
- ユーザー入力の適切なサニタイゼーション

#### 3. パフォーマンス最適化
- 不要なクエリを避けるためのEager Loading
- キャッシュの適切な活用
- 画像・アセットの最適化

## Bladeテンプレート実装

### 基本構造
```blade
{{-- resources/views/layouts/app.blade.php --}}
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        @include('layouts.navigation')

        <!-- Page Heading -->
        @if (isset($header))
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main>
            {{ $slot }}
        </main>
    </div>
</body>
</html>
```

### コンポーネント作成
```blade
{{-- resources/views/components/staff-shift-card.blade.php --}}
@props(['shift'])

<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 text-gray-900">
        <div class="flex justify-between items-start">
            <div>
                <h3 class="text-lg font-semibold">
                    {{ $shift->staff->name }}
                </h3>
                <p class="text-sm text-gray-600">
                    {{ $shift->role_col }}
                </p>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium">
                    {{ $shift->start_time->format('H:i') }} -
                    {{ $shift->end_time->format('H:i') }}
                </p>
                <p class="text-xs text-gray-500">
                    {{ $shift->start_time->format('Y/m/d') }}
                </p>
            </div>
        </div>
    </div>
</div>
```

### フォーム実装
```blade
{{-- resources/views/staff-shifts/create.blade.php --}}
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('シフト作成') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('staff-shifts.store') }}">
                        @csrf

                        <!-- スタッフ選択 -->
                        <div class="mb-4">
                            <x-input-label for="staff_id" :value="__('スタッフ')" />
                            <select id="staff_id" name="staff_id"
                                    class="block mt-1 w-full border-gray-300 rounded-md shadow-sm"
                                    required>
                                <option value="">選択してください</option>
                                @foreach($staffMembers as $staff)
                                    <option value="{{ $staff->id }}"
                                            {{ old('staff_id') == $staff->id ? 'selected' : '' }}>
                                        {{ $staff->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error :messages="$errors->get('staff_id')" class="mt-2" />
                        </div>

                        <!-- 開始時間 -->
                        <div class="mb-4">
                            <x-input-label for="start_time" :value="__('開始時間')" />
                            <x-text-input id="start_time" name="start_time" type="datetime-local"
                                          :value="old('start_time')" required />
                            <x-input-error :messages="$errors->get('start_time')" class="mt-2" />
                        </div>

                        <!-- 終了時間 -->
                        <div class="mb-4">
                            <x-input-label for="end_time" :value="__('終了時間')" />
                            <x-text-input id="end_time" name="end_time" type="datetime-local"
                                          :value="old('end_time')" required />
                            <x-input-error :messages="$errors->get('end_time')" class="mt-2" />
                        </div>

                        <!-- 役割 -->
                        <div class="mb-4">
                            <x-input-label for="role_col" :value="__('役割')" />
                            <select id="role_col" name="role_col"
                                    class="block mt-1 w-full border-gray-300 rounded-md shadow-sm"
                                    required>
                                <option value="">選択してください</option>
                                <option value="cashier" {{ old('role_col') == 'cashier' ? 'selected' : '' }}>
                                    レジ担当
                                </option>
                                <option value="kitchen" {{ old('role_col') == 'kitchen' ? 'selected' : '' }}>
                                    キッチン
                                </option>
                                <option value="floor" {{ old('role_col') == 'floor' ? 'selected' : '' }}>
                                    フロア
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('role_col')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <x-secondary-button type="button" onclick="history.back()">
                                {{ __('キャンセル') }}
                            </x-secondary-button>

                            <x-primary-button class="ml-4">
                                {{ __('作成') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

## セキュリティ対策

### 1. XSS対策
```blade
{{-- 悪い例：エスケープなし --}}
{!! $userInput !!}

{{-- 良い例：自動エスケープ --}}
{{ $userInput }}

{{-- HTMLを含む場合の適切な処理 --}}
{!! Str::markdown($trustedContent) !!}
```

### 2. CSRF対策
```blade
{{-- フォームには必ずCSRFトークンを含める --}}
<form method="POST" action="{{ route('staff-shifts.store') }}">
    @csrf
    <!-- フォーム内容 -->
</form>

{{-- AjaxリクエストでのCSRF対策 --}}
<meta name="csrf-token" content="{{ csrf_token() }}">
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
</script>
```

### 3. 認可チェック
```blade
{{-- 権限チェック --}}
@can('create', App\Models\StaffShift::class)
    <a href="{{ route('staff-shifts.create') }}" class="btn btn-primary">
        新規作成
    </a>
@endcan

{{-- 所有者チェック --}}
@can('update', $shift)
    <a href="{{ route('staff-shifts.edit', $shift) }}" class="btn btn-secondary">
        編集
    </a>
@endcan
```

## エラーハンドリング

### バリデーションエラー表示
```blade
{{-- 個別フィールドエラー --}}
<x-input-error :messages="$errors->get('email')" class="mt-2" />

{{-- 全エラー表示 --}}
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
```

### フラッシュメッセージ
```blade
{{-- 成功メッセージ --}}
@if (session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

{{-- エラーメッセージ --}}
@if (session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif
```

## フロントエンドフレームワーク選択

### 1. Livewire + Flux UI（推奨）
```php
// app/Livewire/StaffShiftManager.php
<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\StaffShift;
use App\Services\StaffShiftService;

class StaffShiftManager extends Component
{
    public $shifts = [];
    public $showCreateForm = false;

    public function mount()
    {
        $this->loadShifts();
    }

    public function loadShifts()
    {
        $this->shifts = StaffShift::with('staff')
            ->orderBy('start_time')
            ->get();
    }

    public function createShift($shiftData)
    {
        $service = new StaffShiftService();
        $service->createShift($shiftData);

        $this->loadShifts();
        $this->showCreateForm = false;

        session()->flash('success', 'シフトが作成されました。');
    }

    public function render()
    {
        return view('livewire.staff-shift-manager');
    }
}
```

```blade
{{-- resources/views/livewire/staff-shift-manager.blade.php --}}
<div>
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">シフト管理</h2>
        <flux:button wire:click="$set('showCreateForm', true)">
            新規作成
        </flux:button>
    </div>

    @if($showCreateForm)
        <flux:modal>
            <flux:modal.header>シフト作成</flux:modal.header>
            <flux:modal.body>
                <!-- フォーム内容 -->
            </flux:modal.body>
        </flux:modal>
    @endif

    <div class="grid gap-4">
        @foreach($shifts as $shift)
            <x-staff-shift-card :shift="$shift" />
        @endforeach
    </div>
</div>
```

### 2. Inertia.js + Vue/React
```php
// app/Http/Controllers/StaffShiftController.php
public function index()
{
    return Inertia::render('StaffShifts/Index', [
        'shifts' => StaffShift::with('staff')
            ->orderBy('start_time')
            ->get()
            ->map(fn ($shift) => [
                'id' => $shift->id,
                'staff_name' => $shift->staff->name,
                'start_time' => $shift->start_time->format('Y-m-d H:i'),
                'end_time' => $shift->end_time->format('Y-m-d H:i'),
                'role_col' => $shift->role_col,
            ]),
    ]);
}
```

```vue
<!-- resources/js/Pages/StaffShifts/Index.vue -->
<template>
    <AppLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                シフト管理
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-medium">シフト一覧</h3>
                            <Link :href="route('staff-shifts.create')"
                                  class="btn btn-primary">
                                新規作成
                            </Link>
                        </div>

                        <div class="grid gap-4">
                            <div v-for="shift in shifts" :key="shift.id"
                                 class="border rounded-lg p-4">
                                <div class="flex justify-between">
                                    <div>
                                        <h4 class="font-semibold">{{ shift.staff_name }}</h4>
                                        <p class="text-sm text-gray-600">{{ shift.role_col }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm">
                                            {{ formatTime(shift.start_time) }} -
                                            {{ formatTime(shift.end_time) }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';

defineProps({
    shifts: Array,
});

const formatTime = (datetime) => {
    return new Date(datetime).toLocaleTimeString('ja-JP', {
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>
```

## パフォーマンス最適化

### 1. Eager Loading
```php
// 悪い例：N+1問題
$shifts = StaffShift::all();
foreach ($shifts as $shift) {
    echo $shift->staff->name; // 各シフトごとにクエリが実行される
}

// 良い例：Eager Loading
$shifts = StaffShift::with('staff')->get();
foreach ($shifts as $shift) {
    echo $shift->staff->name; // 1回のクエリで全スタッフ情報を取得
}
```

### 2. ビューキャッシュ
```php
// config/view.php
'compiled' => env(
    'VIEW_COMPILED_PATH',
    realpath(storage_path('framework/views'))
),

// キャッシュクリア
php artisan view:clear
php artisan view:cache
```

### 3. アセット最適化
```javascript
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', 'axios'],
                },
            },
        },
    },
});
```

## デバッグとトラブルシューティング

### 1. Bladeデバッグ
```blade
{{-- デバッグ情報表示 --}}
@if(config('app.debug'))
    <div class="debug-info">
        <h4>Debug Info:</h4>
        <pre>{{ var_dump($variable) }}</pre>
    </div>
@endif

{{-- Laravel Debugbarの活用 --}}
{{-- composer require barryvdh/laravel-debugbar --dev --}}
```

### 2. エラーログ確認
```bash
# ログファイル確認
tail -f storage/logs/laravel.log

# 特定のエラー検索
grep "ERROR" storage/logs/laravel.log
```

### 3. パフォーマンス監視
```php
// クエリログ有効化
DB::enableQueryLog();

// ビュー内でクエリ確認
@if(config('app.debug'))
    <div class="query-log">
        <h4>Executed Queries:</h4>
        <pre>{{ json_encode(DB::getQueryLog(), JSON_PRETTY_PRINT) }}</pre>
    </div>
@endif
```
