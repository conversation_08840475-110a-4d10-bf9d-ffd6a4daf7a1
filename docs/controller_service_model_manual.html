<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> × Supabase レイヤードアーキテクチャ実践マニュアル</title>
    <style>
        :root {
            --primary: #4099de;
            --secondary: #ff8a65;
            --dark: #2c3e50;
            --light: #ecf0f1;
            --success: #2ecc71;
            --warning: #f39c12;
            --danger: #e74c3c;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --code-bg: #282c34;
            --code-color: #abb2bf;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--light);
            padding: 0;
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            background-color: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            min-height: 100vh;
        }

        header {
            background: linear-gradient(to right, var(--primary), #3471a3);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            text-align: center;
            border-bottom: 5px solid var(--secondary);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        nav {
            background-color: white;
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid var(--gray-300);
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .nav-item {
            padding: 0.5rem 1rem;
            background-color: var(--gray-100);
            border-radius: 50px;
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.2s;
            border: 1px solid var(--gray-300);
        }

        .nav-item:hover {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--dark);
            margin: 1.5rem 0 1rem 0;
            line-height: 1.2;
        }

        h1 {
            font-size: 2.5rem;
            border-bottom: 4px solid var(--primary);
            padding-bottom: 0.5rem;
            margin-top: 0;
        }

        h2 {
            font-size: 2rem;
            border-bottom: 2px solid var(--gray-300);
            padding-bottom: 0.5rem;
            margin-top: 3rem;
            position: relative;
        }

        h2::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 4px;
            background: var(--primary);
            bottom: -2px;
            left: 0;
        }

        h3 {
            font-size: 1.5rem;
            color: var(--primary);
            margin-top: 2rem;
        }

        p {
            margin-bottom: 1.5rem;
            color: var(--gray-700);
        }

        ul, ol {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        section {
            margin-bottom: 3rem;
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        section:target {
            animation: highlight 1s ease-out;
        }

        @keyframes highlight {
            0% { background-color: #fff9c4; }
            100% { background-color: white; }
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        thead {
            background-color: var(--primary);
            color: white;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-300);
        }

        th {
            font-weight: 600;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:nth-child(even) {
            background-color: var(--gray-100);
        }

        code {
            font-family: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
            background-color: var(--gray-100);
            color: var(--primary);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-size: 0.875em;
        }

        pre {
            background-color: var(--code-bg);
            color: var(--code-color);
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 1.5rem 0;
            position: relative;
        }

        pre code {
            background-color: transparent;
            color: inherit;
            padding: 0;
            font-size: 0.9rem;
            line-height: 1.5;
            tab-size: 4;
        }

        pre::before {
            content: attr(data-language);
            position: absolute;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 0 8px 0 8px;
        }

        .language-php::before {
            content: "PHP";
        }

        .language-bash::before {
            content: "Bash";
        }

        .language-plantuml::before {
            content: "PlantUML";
        }

        .architecture-diagram {
            font-family: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
            white-space: pre;
            line-height: 1.2;
            background-color: var(--gray-900);
            color: var(--gray-100);
            padding: 2rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            margin: 2rem 0;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }

        .comparison-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-top: 4px solid var(--primary);
        }

        .comparison-card h3 {
            margin-top: 0;
            color: var(--dark);
            font-size: 1.25rem;
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 0.75rem;
            margin-bottom: 1rem;
        }

        .comparison-card.before {
            border-top-color: var(--danger);
        }

        .comparison-card.before h3 {
            color: var(--danger);
        }

        .comparison-card.after {
            border-top-color: var(--success);
        }

        .comparison-card.after h3 {
            color: var(--success);
        }

        .footer {
            background-color: var(--gray-800);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 4rem;
            border-top: 5px solid var(--primary);
        }

        /* Code Highlighting */
        .keyword {
            color: #c678dd;
        }
        .string {
            color: #98c379;
        }
        .comment {
            color: #7f848e;
            font-style: italic;
        }
        .number {
            color: #d19a66;
        }
        .class {
            color: #e5c07b;
        }
        .function {
            color: #61afef;
        }

        /* Card Design */
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .card-header {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--gray-300);
        }

        .card-title {
            margin: 0;
            color: var(--primary);
        }

        /* Step-by-step list */
        .steps {
            counter-reset: step;
            list-style-type: none;
            padding-left: 0;
            margin: 2rem 0;
        }

        .steps li {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 1.5rem;
            counter-increment: step;
        }

        .steps li:before {
            content: counter(step);
            position: absolute;
            left: 0;
            top: 0;
            width: 2rem;
            height: 2rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 2rem;
            font-weight: bold;
        }

        .steps li:not(:last-child):after {
            content: "";
            position: absolute;
            left: 1rem;
            top: 2rem;
            width: 2px;
            height: calc(100% - 1rem);
            background-color: var(--gray-300);
        }

        /* Note and Warning Boxes */
        .note, .warning, .tip {
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            position: relative;
            padding-left: 4rem;
        }

        .note:before, .warning:before, .tip:before {
            font-family: 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
            position: absolute;
            left: 1.5rem;
            top: 1.5rem;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .note {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .note:before {
            content: "i";
            color: #2196f3;
        }

        .warning {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
        }

        .warning:before {
            content: "!";
            color: #ffc107;
        }

        .tip {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
        }

        .tip:before {
            content: "✓";
            color: #4caf50;
        }

        /* Tags */
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .tag {
            display: inline-block;
            background-color: var(--gray-200);
            color: var(--gray-700);
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .tag-primary {
            background-color: var(--primary);
            color: white;
        }

        .tag-success {
            background-color: var(--success);
            color: white;
        }

        .tag-warning {
            background-color: var(--warning);
            color: white;
        }

        .tag-danger {
            background-color: var(--danger);
            color: white;
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
        }

        #back-to-top.visible {
            opacity: 1;
            transform: translateY(0);
        }

        #back-to-top:hover {
            background-color: var(--secondary);
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <header>
        <h1>Laravel × Supabase 業務アプリ開発マニュアル</h1>
    </header>

    <nav>
        <div class="nav-container">
            <a href="#overview" class="nav-item">概要図</a>
            <a href="#supabase" class="nav-item">Supabase連携</a>
            <a href="#responsibilities" class="nav-item">各レイヤの責務</a>
            <a href="#shift-example" class="nav-item">シフト管理例</a>
            <a href="#sales-example" class="nav-item">売上同期例</a>
            <a href="#code-samples" class="nav-item">サンプルコード</a>
            <a href="#testing" class="nav-item">テスト例</a>
            <a href="#ci" class="nav-item">CI/CD設定</a>
        </div>
    </nav>

    <div class="container">
        <section id="overview">
            <h2>アーキテクチャ概要図</h2>
            <p>Flying Scotsman グループの<strong>シフト管理・売上同期・予約管理</strong>を Laravel API と Supabase で一本化する設計方針を示します。</p>
            <div class="architecture-diagram">
┌────────────┐   Cron   ┌──────────────┐
│Sales       │────────▶│Supabase      │
│SyncJob     │         │Edge Functions │
└────────────┘         └──────────────┘
       ▲                       │
       │HTTP API               │Row Level Security
       ▼                       ▼
┌────────────┐   RPC    ┌────────────┐
│Laravel     │────────▶│Postgres DB │
└────────────┘         └────────────┘</div>

            <div class="note">
                <p>レイヤードアーキテクチャとSupabaseを組み合わせることで、<strong>上位レイヤーが下位レイヤーに依存</strong>する形を維持しながら、モダンなバックエンド構成を実現できます。</p>
            </div>
        </section>

        <section id="supabase">
            <h2>Supabase連携フロー</h2>
            <p>LaravelとSupabaseを組み合わせた開発フローを解説します。初心者にもわかりやすい手順で進められます。</p>

            <ol class="steps">
                <li><strong>ローカル環境起動</strong><br>
                  <code>supabase start</code> でローカルのPostgreSQLが起動します（localhost:54322）</li>
                <li><strong>Laravelマイグレーション実行</strong><br>
                  <code>php artisan migrate</code> でLaravelスキーマをPublicスキーマに適用</li>
                <li><strong>Supabase関数・RLSの適用</strong><br>
                  <code>supabase db push --local --dry-run</code> で変更を検証</li>
                <li><strong>本番デプロイ</strong><br>
                  develop → main のマージ後、CIが自動で <code>supabase db push</code> を実行</li>
            </ol>

            <h3>環境変数設定</h3>
            <table>
                <thead>
                    <tr>
                        <th>環境変数</th>
                        <th>設定例</th>
                        <th>備考</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>DB_CONNECTION</td>
                        <td>pgsql</td>
                        <td>PostgreSQL接続</td>
                    </tr>
                    <tr>
                        <td>DB_HOST</td>
                        <td>localhost</td>
                        <td>ローカル開発時</td>
                    </tr>
                    <tr>
                        <td>DB_PORT</td>
                        <td>54322</td>
                        <td>supabase.tomlの設定に従う</td>
                    </tr>
                    <tr>
                        <td>DB_DATABASE</td>
                        <td>postgres</td>
                        <td>デフォルトDB名</td>
                    </tr>
                    <tr>
                        <td>DB_USERNAME</td>
                        <td>postgres</td>
                        <td>CI/CD時はGitHubシークレットから取得</td>
                    </tr>
                    <tr>
                        <td>DB_PASSWORD</td>
                        <td>postgres</td>
                        <td>同上</td>
                    </tr>
                    <tr>
                        <td>SUPABASE_URL</td>
                        <td>http://localhost:54321</td>
                        <td>APIエンドポイント</td>
                    </tr>
                    <tr>
                        <td>SUPABASE_KEY</td>
                        <td>eyJh...</td>
                        <td>anon keyまたはservice_role key</td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <p>`.env` テンプレートは `example.env` に同梱しています。ローカル開発では <code>supabase status</code> コマンドでキー情報が確認できます。</p>
            </div>
        </section>

        <section id="responsibilities">
            <h2>各レイヤの責務</h2>
            <table>
                <thead>
                    <tr>
                        <th>レイヤー</th>
                        <th>役割</th>
                        <th>責務</th>
                        <th>実装例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Controller</td>
                        <td>リクエスト処理</td>
                        <td>
                            <ul>
                                <li>HTTPリクエストの検証</li>
                                <li>認証・認可チェック</li>
                                <li>Serviceの呼び出し</li>
                                <li>レスポンス生成</li>
                            </ul>
                        </td>
                        <td><code>ShiftController</code>, <code>SalesController</code></td>
                    </tr>
                    <tr>
                        <td>Service</td>
                        <td>ビジネスロジック</td>
                        <td>
                            <ul>
                                <li>ビジネスルールの実装</li>
                                <li>トランザクション管理</li>
                                <li>Supabase RPC呼び出し</li>
                                <li>イベント発火</li>
                            </ul>
                        </td>
                        <td><code>ShiftService</code>, <code>SalesSyncService</code></td>
                    </tr>
                    <tr>
                        <td>Repository</td>
                        <td>データアクセス抽象化</td>
                        <td>
                            <ul>
                                <li>データアクセスロジック</li>
                                <li>クエリビルド</li>
                                <li>Supabase APIラッパー</li>
                                <li>キャッシュ管理</li>
                            </ul>
                        </td>
                        <td><code>ShiftRepository</code>, <code>SalesRepository</code></td>
                    </tr>
                    <tr>
                        <td>Model</td>
                        <td>データ構造</td>
                        <td>
                            <ul>
                                <li>テーブル構造定義</li>
                                <li>属性・リレーション定義</li>
                                <li>スコープ・ミューテター</li>
                                <li>バリデーションルール</li>
                            </ul>
                        </td>
                        <td><code>Shift</code>, <code>StaffMember</code>, <code>Store</code></td>
                    </tr>
                </tbody>
            </table>

            <div class="tip">
                <p>各レイヤーの責務を明確にすることで、Supabase連携部分とLaravelアプリケーションロジックを適切に分離できます。これにより保守性が向上し、新機能の追加もスムーズになります。</p>
            </div>
        </section>

        <section id="shift-example">
            <h2>シフト管理システム実装例</h2>

            <h3>1. モデル定義</h3>
            <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StaffShift extends Model
{
    protected $table = 'staff_shifts';

    protected $fillable = [
        'staff_id',
        'store_id',
        'shift_date',
        'start_time',
        'end_time',
        'role_col', // 予約語回避のため _col サフィックス
        'break_minutes',
        'status',
    ];

    protected $casts = [
        'shift_date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'break_minutes' => 'integer',
    ];

    // リレーションシップ
    public function staff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'staff_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    // シフト時間の計算
    public function getWorkHoursAttribute(): float
    {
        $start = $this->start_time;
        $end = $this->end_time;
        $breakMinutes = $this->break_minutes;

        $workMinutes = $end->diffInMinutes($start) - $breakMinutes;
        return round($workMinutes / 60, 2);
    }
}</code></pre>
<!DOCTYPE html>
<html lang="ja">
<head><meta charset="utf-8"><title>Laravel × Supabase 開発マニュアル</title></head>
<body>
<h1>Laravel × Supabase 開発マニュアル（202505121200 更新）</h1>

<h2>1. レイヤ構成</h2>
<p>Controller → Service → Model → DB の一方向依存。Service に業務ロジック集中。</p>

<h2>2. バージョン固定 <code>config/coding_standards.yml</code></h2>
<pre><code>  # JTT-Apps コーディング標準 (v1.0.0)
  version: 1.0.0
  last_updated: "2025-05-12"
  frameworks:
    laravel:
      version: "12.13.0"
      min_php: "8.2"
      support_end: "2027-02-24"
  languages:
    php:
      version: "8.2"
      style: "PSR-12"
      indent: 4
      max_line_length: 120
    javascript:
      version: "ES2022"
      style: "prettier"
      indent: 2
  architecture:
    pattern: "layered"
    dependencies:
      - "Controller -> Service -> Model -> DB"
    naming:
      classes: "PascalCase"
      methods: "camelCase"
      variables: "camelCase"
      constants: "SNAKE_CASE_CAPS"
      private_props: "camelCase"
      boolean_methods: "is*, has*, can*"
  tools:
    testing:
      - "phpunit"
      - "larastan"
      - "playwright"
    ci:
      - "GitHub Actions"
    local:
      - "supabase-cli"</code></pre>

<h2>3. Supabase 開発フロー</h2>
<ol>
  <li><code>supabase start</code></li>
  <li><code>php artisan migrate</code></li>
  <li><code>supabase db push --local --dry-run</code></li>
  <li>GitHub Actions → 本番 push</li>
</ol>

<h2>4. サンプル Service</h2>
<pre><code class="language-php">&lt;?php
namespace App\Services;
final class ShiftService {
  public function todayWorkers(int $storeId): array {/* … */}
}
</code></pre>

<h2>5. CI & テスト</h2>
<ul>
  <li>phpunit</li>
  <li>larastan</li>
  <li>Playwright smoke</li>
</ul>
</body></html>
            <h3>2. マイグレーションファイル</h3>
            <pre data-language="PHP"><code class="language-php">&lt;?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('staff_shifts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('staff_id')->constrained('users');
            $table->foreignId('store_id')->constrained('stores');
            $table->date('shift_date');
            $table->dateTime('start_time');
            $table->dateTime('end_time');
            $table->string('role_col'); // 予約語を避けるため _col を付加
            $table->integer('break_minutes')->default(0);
            $table->string('status')->default('scheduled'); // scheduled, completed, canceled
            $table->timestamps();

            // インデックス
            $table->index(['store_id', 'shift_date']);
            $table->index(['staff_id', 'shift_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('staff_shifts');
    }
};</code></pre>

            <h3>3. サービスクラス実装</h3>
            <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Services;

use App\Repositories\Interfaces\ShiftRepositoryInterface;
use App\Models\StaffShift;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ShiftService
{
    public function __construct(
        private readonly ShiftRepositoryInterface $shiftRepository
    ) {}

    // 特定店舗の当日シフト取得
    public function getTodayShifts(int $storeId): Collection
    {
        $today = Carbon::today();
        return $this->shiftRepository->findByStoreAndDate($storeId, $today);
    }

    // 週間シフト作成
    public function createWeeklyShift(
        int $staffId,
        int $storeId,
        Carbon $startDate,
        array $shiftData
    ): Collection {
        $createdShifts = collect();

        for ($i = 0; $i < 7; $i++) {
            $shiftDate = $startDate->copy()->addDays($i);
            $dayOfWeek = strtolower($shiftDate->format('l'));

            // その曜日のシフトがあるか確認
            if (isset($shiftData[$dayOfWeek]) && $shiftData[$dayOfWeek]['is_working']) {
                $shift = $this->shiftRepository->create([
                    'staff_id' => $staffId,
                    'store_id' => $storeId,
                    'shift_date' => $shiftDate,
                    'start_time' => $shiftDate->copy()->setTimeFromTimeString($shiftData[$dayOfWeek]['start_time']),
                    'end_time' => $shiftDate->copy()->setTimeFromTimeString($shiftData[$dayOfWeek]['end_time']),
                    'role_col' => $shiftData[$dayOfWeek]['role'] ?? 'staff',
                    'break_minutes' => $shiftData[$dayOfWeek]['break_minutes'] ?? 60,
                    'status' => 'scheduled'
                ]);

                $createdShifts->push($shift);
            }
        }

        return $createdShifts;
    }

    // スタッフの月間労働時間集計
    public function calculateMonthlyHours(int $staffId, int $year, int $month): float
    {
        $startDate = Carbon::createFromDate($year, $month, 1)->startOfDay();
        $endDate = $startDate->copy()->endOfMonth();

        $shifts = $this->shiftRepository->findByStaffAndDateRange($staffId, $startDate, $endDate);

        return $shifts->sum(function (StaffShift $shift) {
            return $shift->work_hours;
        });
    }
}</code></pre>

            <h3>4. Supabase RPC関数連携例</h3>
            <pre data-language="SQL"><code class="language-sql">-- supabase/migrations/20240513103000_create_functions.sql
CREATE OR REPLACE FUNCTION public.get_store_shifts(p_store_id bigint, p_date date)
RETURNS TABLE (
    id bigint,
    staff_id bigint,
    staff_name text,
    shift_date date,
    start_time timestamptz,
    end_time timestamptz,
    role_col text,
    break_minutes integer,
    work_hours numeric
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.staff_id,
        u.name as staff_name,
        s.shift_date,
        s.start_time,
        s.end_time,
        s.role_col,
        s.break_minutes,
        (EXTRACT(EPOCH FROM (s.end_time - s.start_time))/3600 - (s.break_minutes/60.0))::numeric(5,2) as work_hours
    FROM
        staff_shifts s
    JOIN
        users u ON s.staff_id = u.id
    WHERE
        s.store_id = p_store_id
        AND s.shift_date = p_date
        AND s.status = 'scheduled'
    ORDER BY
        s.start_time ASC;
END;
$$;</code></pre>
        </section>

        <section id="sales-example">
            <h2>売上同期システム実装例</h2>

            <h3>1. JotForm Webhook処理</h3>
            <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Jobs;

use App\Services\SalesSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessJotFormWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private readonly array $formData
    ) {}

    public function handle(SalesSyncService $salesService): void
    {
        try {
            // Supabase RPC経由でデータ同期
            $result = $salesService->syncSalesData(
                storeId: (int) $this->formData['storeId'] ?? 0,
                date: $this->formData['date'] ?? date('Y-m-d'),
                salesAmount: (float) $this->formData['amount'] ?? 0,
                customerCount: (int) $this->formData['customers'] ?? 0,
                itemsSold: $this->extractItems($this->formData['items'] ?? '[]')
            );
<!DOCTYPE html><html lang="ja"><head><meta charset="utf-8">
<title>Laravel × Supabase 開発マニュアル</title></head><body>
<h1>Laravel × Supabase 開発マニュアル（202505121200 更新）</h1>
<h2>1. バージョン固定</h2>
<ul><li>PHP 8.3.*</li><li>Laravel 12.x</li><li>Supabase CLI 1.154.0</li></ul>
<h2>2. 開発フロー</h2>
<ol><li><code>supabase start</code></li><li><code>php artisan migrate</code></li>
<li><code>supabase db push --local --dry-run</code></li><li>CI → 本番 push</li></ol>
<h2>3. サンプル Service</h2>
<pre><code class="language-php">&lt;?php
namespace App\Services; final class ShiftService { /* … */ }</code></pre>
<h2>4. CI & テスト</h2>
<ul><li>phpunit</li><li>larastan</li><li>Playwright smoke</li></ul>
</body></html>
            Log::info('JotForm data synced successfully', [
                'store_id' => $this->formData['storeId'] ?? 0,
                'result' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process JotForm webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $this->formData
            ]);

            // リトライ設定に応じて失敗を通知
            $this->fail($e);
        }
    }

    private function extractItems(string $itemsJson): array
    {
        try {
            $items = json_decode($itemsJson, true, 512, JSON_THROW_ON_ERROR);
            return is_array($items) ? $items : [];
        } catch (\JsonException $e) {
            Log::warning('Invalid items JSON', [
                'error' => $e->getMessage(),
                'raw' => $itemsJson
            ]);
            return [];
        }
    }
}</code></pre>

            <h3>2. Supabase連携サービスクラス</h3>
            <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class SalesSyncService
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => env('SUPABASE_URL'),
            'headers' => [
                'apikey' => env('SUPABASE_KEY'),
                'Authorization' => 'Bearer ' . env('SUPABASE_KEY'),
                'Content-Type' => 'application/json'
            ]
        ]);
    }

    /**
     * 売上データをSupabase RPCに同期
     */
    public function syncSalesData(
        int $storeId,
        string $date,
        float $salesAmount,
        int $customerCount,
        array $itemsSold = []
    ): array {
        try {
            $response = $this->client->post('/rest/v1/rpc/sync_sales_data', [
                'json' => [
                    'p_store_id' => $storeId,
                    'p_date' => $date,
                    'p_sales_amount' => $salesAmount,
                    'p_customer_count' => $customerCount,
                    'p_items_sold' => json_encode($itemsSold)
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result ?? ['success' => true];
        } catch (\Exception $e) {
            Log::error('Supabase RPC call failed', [
                'error' => $e->getMessage(),
                'store_id' => $storeId,
                'date' => $date
            ]);

            throw new \RuntimeException('売上データの同期に失敗しました: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 月次レポートを取得
     */
    public function getMonthlyReport(int $storeId, int $year, int $month): array
    {
        try {
            $response = $this->client->post('/rest/v1/rpc/get_monthly_sales_report', [
                'json' => [
                    'p_store_id' => $storeId,
                    'p_year' => $year,
                    'p_month' => $month
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true) ?? [];
        } catch (\Exception $e) {
            Log::error('Failed to fetch monthly report', [
                'error' => $e->getMessage(),
                'store_id' => $storeId,
                'year_month' => "$year-$month"
            ]);

            return [
                'error' => '月次レポートの取得に失敗しました',
                'details' => $e->getMessage()
            ];
        }
    }
}</code></pre>

            <h3>3. Supabase側のRPC関数定義</h3>
            <pre data-language="SQL"><code class="language-sql">-- supabase/migrations/20250511000002_create_procedures.sql
CREATE OR REPLACE FUNCTION public.sync_sales_data(
    p_store_id bigint,
    p_date date,
    p_sales_amount numeric,
    p_customer_count integer,
    p_items_sold jsonb
) RETURNS jsonb
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    v_sales_id bigint;
    v_item record;
BEGIN
    -- 既存レコードの確認
    SELECT id INTO v_sales_id
    FROM core.daily_sales
    WHERE store_id = p_store_id AND sales_date = p_date;

    -- 登録または更新
    IF v_sales_id IS NULL THEN
        -- 新規レコード作成
        INSERT INTO core.daily_sales
            (store_id, sales_date, total_amount, customer_count, created_at, updated_at)
        VALUES
            (p_store_id, p_date, p_sales_amount, p_customer_count, now(), now())
        RETURNING id INTO v_sales_id;
    ELSE
        -- 既存レコード更新
        UPDATE core.daily_sales
        SET total_amount = p_sales_amount,
            customer_count = p_customer_count,
            updated_at = now()
        WHERE id = v_sales_id;
    END IF;

    -- 関連商品データ削除
    DELETE FROM core.sales_items WHERE sales_id = v_sales_id;

    -- 商品データ登録
    IF p_items_sold IS NOT NULL AND jsonb_array_length(p_items_sold) > 0 THEN
        FOR v_item IN SELECT * FROM jsonb_array_elements(p_items_sold)
        LOOP
            INSERT INTO core.sales_items
                (sales_id, item_name, quantity, unit_price, created_at, updated_at)
            VALUES
                (
                    v_sales_id,
                    v_item.value->>'name',
                    (v_item.value->>'quantity')::integer,
                    (v_item.value->>'price')::numeric,
                    now(),
                    now()
                );
        END LOOP;
    END IF;

    RETURN jsonb_build_object(
        'success', true,
        'sales_id', v_sales_id,
        'message', 'Sales data synchronized successfully'
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'code', SQLSTATE
        );
END;
$$;</code></pre>
        </section>

        <section id="code-samples">
            <h2>コントローラ実装例</h2>

            <div class="comparison">
                <div class="comparison-card after">
                    <h3>1. APIコントローラ</h3>
                    <div class="tags">
                        <span class="tag tag-success">シンプル</span>
                        <span class="tag">入出力処理のみ</span>
                    </div>
                    <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\StoreShiftRequest;
use App\Http\Resources\ShiftResource;
use App\Services\ShiftService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ShiftController extends Controller
{
    public function __construct(
        private readonly ShiftService $shiftService
    ) {}

    public function index(int $storeId): ResourceCollection
    {
        $shifts = $this->shiftService->getTodayShifts($storeId);
        return ShiftResource::collection($shifts);
    }

    public function store(StoreShiftRequest $request): JsonResponse
    {
        try {
            $startDate = Carbon::parse($request->validated('start_date'));

            $shifts = $this->shiftService->createWeeklyShift(
                staffId: $request->validated('staff_id'),
                storeId: $request->validated('store_id'),
                startDate: $startDate,
                shiftData: $request->validated('shifts')
            );

            return response()->json([
                'message' => 'シフトが正常に作成されました',
                'shifts' => ShiftResource::collection($shifts)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'シフト作成中にエラーが発生しました',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}</code></pre>
                </div>

                <div class="comparison-card after">
                    <h3>2. Supabase連携コントローラ</h3>
                    <div class="tags">
                        <span class="tag tag-primary">RPC呼び出し</span>
                        <span class="tag">レスポンス加工</span>
                    </div>
                    <pre data-language="PHP"><code class="language-php">&lt;?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\SalesReportResource;
use App\Services\SalesSyncService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SalesController extends Controller
{
    public function __construct(
        private readonly SalesSyncService $salesService
    ) {}

    public function monthlyReport(Request $request, int $storeId): JsonResponse
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2050',
            'month' => 'required|integer|min:1|max:12'
        ]);

        $year = (int) $request->input('year');
        $month = (int) $request->input('month');

        try {
            $report = $this->salesService->getMonthlyReport($storeId, $year, $month);

            if (isset($report['error'])) {
                return response()->json([
                    'message' => $report['error'],
                    'details' => $report['details'] ?? null
                ], 500);
            }

            return response()->json([
                'data' => SalesReportResource::collection($report),
                'meta' => [
                    'store_id' => $storeId,
                    'year' => $year,
                    'month' => $month,
                    'total_sales' => array_sum(array_column($report, 'total_amount')),
                    'total_customers' => array_sum(array_column($report, 'customer_count'))
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'レポート取得に失敗しました',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}</code></pre>
                </div>
            </div>
        </section>

        <section id="testing">
            <h2>Pest テスト例</h2>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">1. シフトサービステスト</h3>
                    <div class="tags">
                        <span class="tag tag-primary">ユニットテスト</span>
                        <span class="tag">モック</span>
                    </div>
                </div>
                <pre data-language="PHP"><code class="language-php">&lt;?php

use App\Services\ShiftService;
use App\Repositories\Interfaces\ShiftRepositoryInterface;
use App\Models\StaffShift;
use Carbon\Carbon;
use Mockery;
use Illuminate\Support\Collection;

beforeEach(function () {
    $this->repository = Mockery::mock(ShiftRepositoryInterface::class);
    $this->service = new ShiftService($this->repository);
});

it('取得当日のシフトを店舗IDで取得できる', function () {
    // 固定の日付を使用
    Carbon::setTestNow(Carbon::parse('2025-05-15'));

    // モックの設定
    $expectedShifts = collect([
        new StaffShift([
            'id' => 1,
            'staff_id' => 101,
            'store_id' => 1,
            'shift_date' => Carbon::today(),
            'start_time' => Carbon::today()->setTime(9, 0),
            'end_time' => Carbon::today()->setTime(17, 0),
            'role_col' => 'manager',
            'break_minutes' => 60
        ]),
        new StaffShift([
            'id' => 2,
            'staff_id' => 102,
            'store_id' => 1,
            'shift_date' => Carbon::today(),
            'start_time' => Carbon::today()->setTime(13, 0),
            'end_time' => Carbon::today()->setTime(21, 0),
            'role_col' => 'staff',
            'break_minutes' => 60
        ])
    ]);

    $this->repository->shouldReceive('findByStoreAndDate')
        ->once()
        ->with(1, Carbon::today())
        ->andReturn($expectedShifts);

    // テスト実行
    $result = $this->service->getTodayShifts(1);

    // アサーション
    expect($result)->toBeInstanceOf(Collection::class);
    expect($result)->toHaveCount(2);
    expect($result[0]->staff_id)->toBe(101);
    expect($result[1]->staff_id)->toBe(102);
});</code></pre>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">2. Supabase連携テスト</h3>
                    <div class="tags">
                        <span class="tag tag-primary">モックHTTP</span>
                        <span class="tag">API連携</span>
                    </div>
                </div>
                <pre data-language="PHP"><code class="language-php">&lt;?php

use App\Services\SalesSyncService;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\App;

beforeEach(function () {
    // Guzzleモックの設定
    $mock = new MockHandler([
        new Response(200, [], json_encode([
            'success' => true,
            'sales_id' => 123,
            'message' => 'Sales data synchronized successfully'
        ]))
    ]);

    $handlerStack = HandlerStack::create($mock);
    $client = new Client(['handler' => $handlerStack]);

    // Guzzleクライアントをモックに差し替え
    App::instance(Client::class, $client);

    $this->service = new SalesSyncService();
});

it('売上データをSupabase RPCに同期できる', function () {
    $result = $this->service->syncSalesData(
        storeId: 1,
        date: '2025-05-15',
        salesAmount: 125000.50,
        customerCount: 45,
        itemsSold: [
            ['name' => 'コーヒー', 'quantity' => 150, 'price' => 400],
            ['name' => 'サンドイッチ', 'quantity' => 75, 'price' => 600]
        ]
    );

    // レスポンスの検証
    expect($result)->toBeArray();
    expect($result['success'])->toBeTrue();
    expect($result['sales_id'])->toBe(123);
});</code></pre>
            </div>
        </section>

        <section id="ci">
            <h2>CI/CD 設定例</h2>
            <p>GitHub Actionsを使用して、LaravelアプリケーションとSupabaseの連携をCI/CDパイプラインで自動化する設定例です。</p>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">tests.yml</h3>
                    <div class="tags">
                        <span class="tag tag-primary">GitHub Actions</span>
                        <span class="tag">Supabase CLI</span>
                    </div>
                </div>
                <pre data-language="YAML"><code class="language-yaml">name: Laravel & Supabase Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  laravel-tests:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, pgsql
        coverage: xdebug

    - name: Setup Supabase CLI
      uses: actions/cache@v3
      id: supabase-cache
      with:
        path: ~/.supabase/bin
        key: ${{ runner.os }}-supabase-1.154.0
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            text-align: center;
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background-color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .card-description {
            color: var(--gray-600);
            margin-bottom: 1rem;
            flex-grow: 1;
        }

        .card-link {
            background-color: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            transition: background-color 0.3s;
            text-align: center;
        }

        .card-link:hover {
            background-color: var(--gray-700);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="#">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <h1 class="title">Laravel × Supabase 開発マニュアル</h1>
        <p class="subtitle">PHP 8.3.* + Laravel 12.x の効率的な開発ガイド</p>

        <section id="introduction" class="section">
            <h2 class="section-title">はじめに</h2>
            <p>
                このマニュアルは、JTTアプリケーション開発におけるベストプラクティスを初学者にもわかりやすく解説しています。
                Laravel 12.xとSupabaseを組み合わせた効率的な開発手法と、プロジェクト特有の規約について学びましょう。
            </p>
            <div class="highlight">
                <p><strong>このマニュアルの目的:</strong></p>
                <ul>
                    <li>開発者間での一貫した実装手法の確立</li>
                    <li>新規参画者への速やかな知識移転</li>
                    <li>保守性と拡張性の高いコード構造の維持</li>
                </ul>
            </div>
        </section>

        <section id="versions" class="section">
            <h2 class="section-title">バージョン固定</h2>
            <p>
                プロジェクト全体で統一されたバージョンを使用することで、「自分の環境では動くのに」という問題を防ぎます。
                以下のバージョンは<strong>厳格に固定</strong>されており、開発・CI環境でも同一バージョンが使用されます。
            </p>
            <table>
                <thead>
                    <tr>
                        <th>技術</th>
                        <th>バージョン</th>
                        <th>備考</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PHP</td>
                        <td><code>8.3.*</code></td>
                        <td>8.4は非対応（厳格に8.3系に固定）</td>
                    </tr>
                    <tr>
                        <td>Laravel</td>
                        <td><code>12.x</code></td>
                        <td>セキュリティサポートは2027年2月24日まで</td>
                    </tr>
                    <tr>
                        <td>Supabase CLI</td>
                        <td><code>1.154.0</code></td>
                        <td>ローカル開発環境で必須</td>
                    </tr>
                </tbody>
            </table>
            <div class="highlight">
                <p><strong>なぜバージョンを固定するのか:</strong></p>
                <p>バージョン固定により、開発環境の差異によるバグを未然に防ぎ、CI/CDパイプラインの一貫性も確保できます。<code>.tool-versions</code>と<code>composer.json</code>の両方でPHPバージョンを固定しています。</p>
            </div>
        </section>

        <section id="guides" class="section">
            <h2 class="section-title">開発ガイド</h2>
            <p>
                JTTアプリの開発に必要な各種ガイドを用意しています。
                各カードをクリックして、詳細な説明にアクセスしてください。
            </p>

            <div class="card-grid">
                <div class="card">
                    <div class="card-icon">📐</div>
                    <h3 class="card-title">アーキテクチャガイド</h3>
                    <p class="card-description">
                        レイヤードアーキテクチャの詳細と、各レイヤの責務について解説します。
                        フォルダ構造やコンポーネント間の依存関係について学びましょう。
                    </p>
                    <a href="architecture_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🎮</div>
                    <h3 class="card-title">コントローラー＆サービス</h3>
                    <p class="card-description">
                        コントローラーとサービス層の正しい実装方法と責務分離について解説します。
                        良い例と悪い例を比較して効率的な設計を学びましょう。
                    </p>
                    <a href="controller_service_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🗄️</div>
                    <h3 class="card-title">モデルガイド</h3>
                    <p class="card-description">
                        Eloquentモデルの効率的な実装方法とリレーションシップの設計について解説します。
                        アクセサやミューテタの活用法を学びましょう。
                    </p>
                    <a href="models_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🛣️</div>
                    <h3 class="card-title">ルート＆API設計</h3>
                    <p class="card-description">
                        WebとAPIのルート設計の原則と実装手法について解説します。
                        RESTful APIの設計原則と命名規則について学びましょう。
                    </p>
                    <a href="routes_api_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🔌</div>
                    <h3 class="card-title">Supabase連携</h3>
                    <p class="card-description">
                        LaravelとSupabaseの効率的な連携方法と認証・ストレージの活用について解説します。
                        ローカル開発環境の構築方法も学びましょう。
                    </p>
                    <a href="supabase_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🧪</div>
                    <h3 class="card-title">テスト戦略</h3>
                    <p class="card-description">
                        単体テスト、統合テスト、E2Eテストの実装方法と効率的なテスト戦略について解説します。
                        自動テストの重要性を学びましょう。
                    </p>
                    <a href="testing_guide.html" class="card-link">詳細を見る</a>
                </div>
            </div>
        </section>

        <section id="workflow" class="section">
            <h2 class="section-title">開発ワークフロー</h2>
            <p>
                JTTアプリの開発作業は以下のフローに従って進めます。
                ローカル環境からCI/CDを通じた本番デプロイまでの流れを確認しましょう。
            </p>
            <ol>
                <li><strong>環境構築</strong>：<code>PHP 8.3.*</code>とSupabase CLIをインストール</li>
                <li><strong>ローカルデータベース起動</strong>：<code>supabase start</code></li>
                <li><strong>マイグレーション実行</strong>：<code>php artisan migrate</code></li>
                <li><strong>スキーマ更新確認</strong>：<code>supabase db push --local --dry-run</code></li>
                <li><strong>機能実装</strong>：コントローラー、サービス、モデルの実装</li>
                <li><strong>ユニットテスト作成</strong>：<code>php artisan test</code></li>
                <li><strong>静的解析</strong>：<code>./vendor/bin/phpstan analyse</code></li>
                <li><strong>E2Eテスト</strong>：<code>npm run test:e2e</code></li>
                <li><strong>CI実行</strong>：GitHub Actions経由でテスト実行</li>
                <li><strong>本番デプロイ</strong>：CI GREENからの自動デプロイ</li>
            </ol>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="docs/CodingStandards.md">コーディング規約</a>
                <a href="docs/CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="docs/Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Tab functionality for any future tabs
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');

                // Hide all tab contents
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Deactivate all tab buttons
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Activate clicked tab and its content
                button.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    </script>
</body>
</html>
    - name: Install Supabase CLI
      if: steps.supabase-cache.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/.supabase/bin
        wget -O ~/.supabase/bin/supabase https://github.com/supabase/cli/releases/download/v1.154.0/supabase_1.154.0_linux_amd64.tar.gz
        tar -xf ~/.supabase/bin/supabase -C ~/.supabase/bin
        chmod +x ~/.supabase/bin/supabase
        echo "~/.supabase/bin" >> $GITHUB_PATH

    - name: Get Composer Cache Directory
      id: composer-cache
      run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-interaction

    - name: Copy .env
      run: cp .env.example .env

    - name: Generate key
      run: php artisan key:generate

    - name: Directory permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Run migrations
      run: php artisan migrate
      env:
        DB_CONNECTION: pgsql
        DB_HOST: localhost
        DB_PORT: 5432
        DB_DATABASE: postgres
        DB_USERNAME: postgres
        DB_PASSWORD: postgres

    - name: Run Supabase DB Push (dry-run)
      run: supabase db push --local --dry-run
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    - name: Run Larastan Analysis
      run: ./vendor/bin/phpstan analyse

    - name: Execute tests (Unit and Feature tests) via Pest
      run: vendor/bin/pest
      env:
        DB_CONNECTION: pgsql
        DB_HOST: localhost
        DB_PORT: 5432
        DB_DATABASE: postgres
        DB_USERNAME: postgres
        DB_PASSWORD: postgres</code></pre>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">weekly-db-dump.yml</h3>
                    <div class="tags">
                        <span class="tag tag-primary">週次バックアップ</span>
                        <span class="tag">S3保存</span>
                    </div>
                </div>
                <pre data-language="YAML"><code class="language-yaml">name: Weekly DB Dump

on:
  schedule:
    - cron: '0 0 * * 0'  # 毎週日曜日の午前0時に実行
  workflow_dispatch:  # 手動実行用

jobs:
  backup:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'  # mainブランチのみ実行

    steps:
    - uses: actions/checkout@v3

    - name: Setup Supabase CLI
      uses: actions/cache@v3
      id: supabase-cache
      with:
        path: ~/.supabase/bin
        key: ${{ runner.os }}-supabase-1.154.0

    - name: Install Supabase CLI
      if: steps.supabase-cache.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/.supabase/bin
        wget -O ~/.supabase/bin/supabase https://github.com/supabase/cli/releases/download/v1.154.0/supabase_1.154.0_linux_amd64.tar.gz
        tar -xf ~/.supabase/bin/supabase -C ~/.supabase/bin
        chmod +x ~/.supabase/bin/supabase
        echo "~/.supabase/bin" >> $GITHUB_PATH

    - name: Create DB Dump
      run: |
        DATE=$(date +%Y%m%d)
        mkdir -p ./backups
        supabase db dump -f ./backups/backup-$DATE.sql
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

    - name: Upload to S3
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-northeast-1

    - name: Copy to S3
      if: env.AWS_ACCESS_KEY_ID != ''
      run: |
        DATE=$(date +%Y%m%d)
        aws s3 cp ./backups/backup-$DATE.sql s3://${{ secrets.S3_BUCKET }}/db-backups/

    - name: Skip S3 upload
      if: env.AWS_ACCESS_KEY_ID == ''
      run: echo "AWS credentials not set, skipping S3 upload"</code></pre>
            </div>
        </section>
    </div>

    <div class="footer">
        <p>最終更新: 2025-05-12</p>
    </div>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Add data-language attribute to pre tags
        document.querySelectorAll('pre').forEach(pre => {
            const codeElement = pre.querySelector('code');
            if (codeElement) {
                const className = codeElement.className;
                const langMatch = className.match(/language-(\w+)/);
                if (langMatch) {
                    pre.setAttribute('data-language', langMatch[1].toUpperCase());
                }
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-item').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                document.querySelector(targetId).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
