# Git・PR規約（シンプルなGitHubフロー）

## ブランチ戦略
### メインブランチ
- `main` - 本番環境用ブランチ（安定版）

### 作業ブランチ
- 機能追加: `feature/{機能名}` (例: `feature/user-authentication`)
- バグ修正: `fix/{バグ内容}` (例: `fix/login-validation`)
- リファクタリング: `refactor/{対象}` (例: `refactor/user-service`)
- その他メンテナンス: `chore/{内容}` (例: `chore/update-dependencies`)

### ワークフロー
1. `main` ブランチから作業ブランチを作成
2. 機能開発・バグ修正を実施
3. `main` へ直接PRを作成
4. CIテスト通過とレビュー承認後にマージ

## コミットメッセージ規約
### プレフィックス
- `feat:` - 新機能追加
- `fix:` - バグ修正
- `docs:` - ドキュメント更新
- `style:` - コードスタイル修正（機能に影響なし）
- `refactor:` - リファクタリング
- `test:` - テスト追加・修正
- `chore:` - その他のメンテナンス

### 形式
```
<type>: <subject>

<body>

<footer>
```

### 例
```
feat: add user authentication system

- Implement login/logout functionality
- Add password validation
- Create user session management

Closes #123
```

## PRプロセス
### PR作成前チェックリスト
- [ ] ローカルでテストが通ることを確認
- [ ] コードスタイルチェック（Laravel Pint）を実行
- [ ] 関連するテストを追加・更新

### PR構成
PRの説明は以下のセクションで構成：

```markdown
## 概要
変更内容の簡潔な説明

## 変更内容
- 具体的な変更点をリスト形式で記載

## テスト
- 追加・修正したテストの説明
- 手動テストの手順（必要に応じて）

## 確認事項
- [ ] テストが通ること
- [ ] コードスタイルが適切であること
- [ ] ドキュメントが更新されていること（必要に応じて）
```

### レビュープロセス
1. 各PRは単一の機能または修正に焦点を当てる
2. コードレビューは少なくとも1人から承認を得る
3. CI緑（lint.yml & tests.yml）を確認する
4. 承認後、マージを実行

### マージ後
- 作業ブランチは削除する
- 必要に応じてリリースノートを更新