# CI/CD・テスト規約

## TDD（テスト駆動開発）の実践

### TDDサイクル
1. **Red**: 失敗するテストを書く
2. **Green**: テストを通すための最小限のコードを書く
3. **Refactor**: コードを改善する

### TDD実装例
```php
// 1. Red: 失敗するテストを書く
it('calculates total working hours correctly', function () {
    $shift = new StaffShift([
        'start_time' => '2025-01-01 09:00:00',
        'end_time' => '2025-01-01 17:00:00',
    ]);

    expect($shift->getTotalHours())->toBe(8.0);
});

// 2. Green: テストを通すコードを書く
class StaffShift extends Model
{
    public function getTotalHours(): float
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        return $end->diffInHours($start);
    }
}

// 3. Refactor: コードを改善する
```

## テスト戦略

### テストピラミッド
- **単体テスト（70%）**: 個々のクラス・メソッドのテスト
- **統合テスト（20%）**: 複数コンポーネントの連携テスト
- **E2Eテスト（10%）**: ユーザー視点でのシステム全体テスト

### テスト種別
1. **単体テスト**: Pestを使用、モックで依存を分離
2. **機能テスト**: HTTPリクエスト/レスポンスのテスト
3. **統合テスト**: データベース含む複数コンポーネントのテスト
4. **E2Eテスト**: Laravel Duskを使用したブラウザテスト

### テスト実行コマンド
```bash
# 全テスト実行
php artisan test

# 特定のテストファイル実行
php artisan test tests/Feature/StaffShiftTest.php

# カバレッジ付きテスト実行
php artisan test --coverage

# 並列テスト実行
php artisan test --parallel
```

## CI環境（GitHub Actions）

### ワークフロー構成
- **テスト**: `tests.yml` - Pest、PHPUnit
- **リンター**: `lint.yml` - Laravel Pint
- **型チェック**: `phpstan.yml` - PHPStan
- **E2Eテスト**: `dusk.yml` - Laravel Dusk

### 環境要件
- PHP 8.3.6
- Node.js 22.x
- Supabase CLI 2.22.12（固定バージョン）
  - GitHub Actionsのワークフローで指定
  - ローカル開発環境でも同一バージョンを使用

## デプロイフロー（シンプルなGitHubフロー）

### 基本フロー
1. `feature/fix/refactor` ブランチ → `main` PR
2. CI緑（全テスト通過）+ レビュー承認
3. `main` へマージ
4. `main` → 本番デプロイ（自動または手動）

### CI/CDパイプライン
```mermaid
graph LR
    A[PR作成] --> B[CI実行]
    B --> C{テスト結果}
    C -->|成功| D[レビュー]
    C -->|失敗| E[修正]
    E --> B
    D --> F{承認}
    F -->|承認| G[マージ]
    F -->|却下| E
    G --> H[本番デプロイ]
```

## 環境設定

### 開発環境
- `.env.local` - ローカル開発用設定
- `supabase start` - ローカルSupabaseインスタンス

### テスト環境
- GitHub Actions環境変数
- インメモリSQLiteデータベース
- モックサービス使用

### 本番環境
- セキュアな環境変数管理
- Supabase Pro Tier
- 監視・ログ設定