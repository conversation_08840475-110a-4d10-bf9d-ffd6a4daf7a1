# Laravel Artisanコマンドガイド
## 必須ツールによる統一された開発手順

### 🎯 基本原則

**Laravel開発においては必ず`php artisan`コマンドを使用してファイル及びディレクトリを作成する**

- 手動でのファイル作成は禁止
- Laravelの標準ツールを最大限活用
- 誰もが同じ手順で開発を進める
- 一貫性のあるファイル構造を維持

## 📋 必須Artisanコマンド一覧

### コントローラー作成
```bash
# 基本コントローラー
php artisan make:controller StaffShiftController

# リソースコントローラー（CRUD操作）
php artisan make:controller StaffShiftController --resource

# APIリソースコントローラー
php artisan make:controller Api/StaffShiftController --api

# 単一アクションコントローラー
php artisan make:controller ProcessShiftController --invokable
```

### モデル作成
```bash
# 基本モデル
php artisan make:model StaffShift

# モデル + マイグレーション
php artisan make:model StaffShift -m

# モデル + マイグレーション + ファクトリー + シーダー
php artisan make:model StaffShift -mfs

# モデル + マイグレーション + ファクトリー + シーダー + コントローラー
php artisan make:model StaffShift -mfsc

# モデル + マイグレーション + ファクトリー + シーダー + リソースコントローラー
php artisan make:model StaffShift -mfscr
```

### サービス・ビジネスロジック作成
```bash
# サービスクラス（カスタムコマンド使用）
php artisan make:service StaffShiftService

# ジョブ（非同期処理）
php artisan make:job ProcessStaffShiftJob

# アクション（単一責任クラス）
php artisan make:action CreateStaffShiftAction
```

### データベース関連
```bash
# マイグレーション
php artisan make:migration create_staff_shifts_table
php artisan make:migration add_role_col_to_staff_shifts_table --table=staff_shifts

# シーダー
php artisan make:seeder StaffShiftSeeder

# ファクトリー
php artisan make:factory StaffShiftFactory
```

### バリデーション・リクエスト
```bash
# フォームリクエスト
php artisan make:request StoreStaffShiftRequest
php artisan make:request UpdateStaffShiftRequest
```

### 認証・認可
```bash
# ポリシー
php artisan make:policy StaffShiftPolicy

# ミドルウェア
php artisan make:middleware CheckShiftOwnership
```

### テスト作成
```bash
# 機能テスト
php artisan make:test StaffShiftTest

# 単体テスト
php artisan make:test StaffShiftServiceTest --unit

# Pestテスト
php artisan make:test StaffShiftTest --pest
```

### Livewireコンポーネント
```bash
# Livewireコンポーネント
php artisan make:livewire StaffShiftManager

# Livewireコンポーネント（インライン）
php artisan make:livewire StaffShiftCard --inline
```

### その他のコンポーネント
```bash
# イベント
php artisan make:event ShiftCreated

# リスナー
php artisan make:listener SendShiftNotification

# メール
php artisan make:mail ShiftCreatedMail

# 通知
php artisan make:notification ShiftCreatedNotification

# リソース（API）
php artisan make:resource StaffShiftResource
php artisan make:resource StaffShiftCollection

# 例外
php artisan make:exception ShiftOverlapException

# ルール（カスタムバリデーション）
php artisan make:rule NoShiftOverlap
```

## 🔧 開発ワークフロー（Artisanコマンド使用）

### 1. 新機能開発の標準手順

#### Step 1: モデルとマイグレーション作成
```bash
# モデル、マイグレーション、ファクトリー、シーダーを一括作成
php artisan make:model StaffShift -mfs
```

#### Step 2: コントローラー作成
```bash
# APIリソースコントローラー作成
php artisan make:controller Api/StaffShiftController --api
```

#### Step 3: サービスクラス作成
```bash
# ビジネスロジック用サービス作成
php artisan make:service StaffShiftService
```

#### Step 4: バリデーション作成
```bash
# フォームリクエスト作成
php artisan make:request StoreStaffShiftRequest
php artisan make:request UpdateStaffShiftRequest
```

#### Step 5: ポリシー作成
```bash
# 認可ポリシー作成
php artisan make:policy StaffShiftPolicy
```

#### Step 6: テスト作成
```bash
# 機能テスト作成
php artisan make:test StaffShiftTest

# 単体テスト作成
php artisan make:test StaffShiftServiceTest --unit
```

### 2. 実装例：完全なCRUD機能作成

```bash
# 1. 全ファイルを一括作成
php artisan make:model StaffShift -mfscr

# 2. APIコントローラー作成
php artisan make:controller Api/StaffShiftController --api

# 3. サービスクラス作成
php artisan make:service StaffShiftService

# 4. フォームリクエスト作成
php artisan make:request StoreStaffShiftRequest
php artisan make:request UpdateStaffShiftRequest

# 5. ポリシー作成
php artisan make:policy StaffShiftPolicy

# 6. APIリソース作成
php artisan make:resource StaffShiftResource

# 7. テスト作成
php artisan make:test StaffShiftTest
php artisan make:test StaffShiftServiceTest --unit

# 8. Livewireコンポーネント作成（必要に応じて）
php artisan make:livewire StaffShiftManager
```

## 📁 生成されるファイル構造

### 標準的なファイル配置
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Api/
│   │   │   └── StaffShiftController.php      # php artisan make:controller
│   │   └── StaffShiftController.php
│   ├── Requests/
│   │   ├── StoreStaffShiftRequest.php        # php artisan make:request
│   │   └── UpdateStaffShiftRequest.php
│   └── Resources/
│       └── StaffShiftResource.php            # php artisan make:resource
├── Models/
│   └── StaffShift.php                        # php artisan make:model
├── Services/
│   └── StaffShiftService.php                 # php artisan make:service
├── Policies/
│   └── StaffShiftPolicy.php                  # php artisan make:policy
└── Livewire/
    └── StaffShiftManager.php                 # php artisan make:livewire

database/
├── migrations/
│   └── 2025_01_15_000000_create_staff_shifts_table.php  # php artisan make:migration
├── factories/
│   └── StaffShiftFactory.php                # php artisan make:factory
└── seeders/
    └── StaffShiftSeeder.php                  # php artisan make:seeder

tests/
├── Feature/
│   └── StaffShiftTest.php                    # php artisan make:test
└── Unit/
    └── StaffShiftServiceTest.php             # php artisan make:test --unit
```

## 🚫 禁止事項

### ❌ 絶対にやってはいけないこと
1. **手動ファイル作成**: `touch`コマンドや手動でのファイル作成
2. **不適切な配置**: Laravelの規約に従わないファイル配置
3. **命名規則違反**: Artisanコマンドが生成する命名規則を無視
4. **スタブの無視**: 生成されたテンプレートを完全に削除

### ✅ 正しいアプローチ
1. **Artisanコマンド使用**: 必ずArtisanコマンドでファイル生成
2. **規約遵守**: Laravelの命名規則とディレクトリ構造を維持
3. **テンプレート活用**: 生成されたスタブを基に実装
4. **一貫性維持**: チーム全体で同じ手順を使用

## 🔄 カスタムArtisanコマンド

### プロジェクト専用コマンド作成
```bash
# カスタムコマンド作成
php artisan make:command MakeServiceCommand

# 使用例
php artisan make:service UserService
```

### カスタムスタブの活用
```bash
# スタブファイルの公開
php artisan stub:publish

# カスタムスタブの編集
# stubs/controller.stub
# stubs/model.stub
```

## 📝 コマンド実行ログ

### 開発時の記録推奨
```bash
# 実行したコマンドをREADMEやドキュメントに記録
echo "php artisan make:model StaffShift -mfscr" >> COMMANDS.md
echo "php artisan make:service StaffShiftService" >> COMMANDS.md
```

## 🎯 品質チェック

### Artisanコマンドによる品質確保
```bash
# ルートキャッシュ
php artisan route:cache

# 設定キャッシュ
php artisan config:cache

# ビューキャッシュ
php artisan view:cache

# 最適化
php artisan optimize

# キャッシュクリア
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## 📚 参考情報

### Artisanコマンド一覧確認
```bash
# 全コマンド表示
php artisan list

# make系コマンド表示
php artisan list make

# 特定コマンドのヘルプ
php artisan help make:model
```

### IDE統合
- **VSCode**: Laravel Artisan拡張機能
- **PHPStorm**: Laravel Plugin
- **コマンドパレット**: Ctrl+Shift+P → Laravel Artisan

---

> 🛠 **重要**: このガイドに従うことで、チーム全体が一貫した手順でLaravel開発を進められます。Artisanコマンドの活用により、品質と効率性を両立できます。
