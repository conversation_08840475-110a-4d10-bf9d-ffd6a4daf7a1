# Supabase コーディング規約

## 環境要件
- Supabase CLI 2.22.12（固定バージョン）
  - CI/CD環境およびローカル開発環境で同一バージョンを使用
  - `.tool-versions`ファイルに記載

## スキーマ & テーブル
- core.* … ビジネスロジックの実態（RLS 有効・直接操作はサービスロールのみ）
- public.* … 外部公開ビュー／RPC ラッパー専用スキーマ（RLS 基本無効）
- 新規テーブルは基本 core.<entity_plural> で作成
- カラム名は snake_case、タイムスタンプは *_at（例: start_at）

## 関数（RPC / 内部）
- 内部ロジック: core.<verb>_<object> (例: core.get_today_shift)
  * 引数には p_ 接頭辞
- 公開ラッパー: public.<same_name>_api (例: public.get_today_shift_api)
  * 末尾 _api 必須
- 必ず引数名に p_ プレフィックス（例: p_store text）
- 公開ラッパーは grant execute on function … to anon, authenticated を付与

## パラメータ命名
- p_<name> … SQL 関数引数用（例: p_user_id）
- Laravel 側パラメータは camelCase 可（storeId など）

## ラッパー実装指針
1. core 関数で複雑処理 → 小さく保守
2. public ラッパーは引数透過 + RLS 無効アクセスのみ
3. Laravel からは select * from public.xxx_api(...) で呼び出し

## RLS ポリシー
- core テーブル – 最低限 select ポリシーを定義（サービスロール以外不可）
- public ラッパー – RLS OFF（ラッパー側でバリデーションする）

## マイグレーション
- ファイル名: YYYYMMDDHHMM_<description>.sql
- DDL と DML を分割（データ投入は Seeder に寄せる）
- supabase db push で CI 連携（tests.yml → future playbook.yml）