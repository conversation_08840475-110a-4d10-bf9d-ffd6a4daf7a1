# クイックスタートガイド
## 新規参画者（AIエージェント・人間開発者）向け

### 🎯 このガイドの目的
JTT-Appsプロジェクトに新規参画する方が、迷わず効率的に開発を開始できるよう、最短ルートを提供します。

## 🚀 5分でスタート

### 1. 最初に読むファイル（必読）
```
📖 README.md → CONTRIBUTING.md → AGENTS.md
```

### 2. 環境構築（10分）
```bash
# 1. リポジトリクローン
git clone https://github.com/ShintaroKawakami/jtt-apps.git
cd jtt-apps

# 2. 依存関係インストール
composer install
npm install

# 3. 環境設定
cp .env.example .env.local
php artisan key:generate

# 4. Supabase起動
supabase start

# 5. データベースセットアップ
php artisan migrate

# 6. 開発サーバー起動
php artisan serve
npm run dev
```

### 3. 動作確認（2分）
```bash
# テスト実行
php artisan test

# E2Eテスト実行
php artisan dusk

# コード品質チェック
./vendor/bin/pint
./vendor/bin/phpstan analyse
```

## 🎭 役割別スタートガイド

### AIエージェント向け
1. **[AGENTS.md](../AGENTS.md)** を熟読
2. **役割分担表** で自分の責任範囲を確認
3. **[docs/Development_Workflow_Guide.md](./Development_Workflow_Guide.md)** でTDDプロセスを理解
4. **最初のIssue** を作成して実践開始

### 人間開発者向け
1. **[README.md](../README.md)** でプロジェクト概要を把握
2. **[CONTRIBUTING.md](../CONTRIBUTING.md)** で貢献プロセスを理解
3. **環境構築** を完了
4. **[docs/TDD_Guide.md](./TDD_Guide.md)** でTDD実践方法を学習
5. **最初のIssue** を作成して開発開始

## 📚 学習パス（推奨順序）

### Phase 1: 基礎理解（30分）
1. **プロジェクト概要**: [README.md](../README.md)
2. **貢献方法**: [CONTRIBUTING.md](../CONTRIBUTING.md)
3. **AIエージェント統合ガイド**: [AGENTS.md](../AGENTS.md)

### Phase 2: 開発プロセス理解（45分）
1. **開発ワークフロー**: [docs/Development_Workflow_Guide.md](./Development_Workflow_Guide.md)
2. **TDD実践**: [docs/TDD_Guide.md](./TDD_Guide.md)
3. **Git・PR規約**: [docs/Git_PR_Guidelines.md](./Git_PR_Guidelines.md)

### Phase 3: 技術仕様理解（60分）
1. **コーディング規約**: [docs/CodingStandards.md](./CodingStandards.md)
2. **セキュリティガイドライン**: [docs/Security_Guidelines.md](./Security_Guidelines.md)
3. **Supabase統合**: [docs/Laravel_Supabase_Integration.md](./Laravel_Supabase_Integration.md)

### Phase 4: 実践開始
1. **Issue作成**: GitHub Issuesで最初のタスクを作成
2. **ブランチ作成**: `git checkout -b feature/issue-番号-内容`
3. **TDD実装**: Red → Green → Refactor サイクル
4. **PR作成**: レビュー依頼

## 🔧 開発環境チェックリスト

### 必須ツール
- [ ] PHP 8.3.6
- [ ] Node.js 22.x
- [ ] Composer
- [ ] Git
- [ ] Supabase CLI 2.22.12

### 推奨ツール
- [ ] VSCode + Laravel拡張
- [ ] PHPStorm
- [ ] Docker Desktop（オプション）

### 環境確認コマンド
```bash
# バージョン確認
php --version
node --version
composer --version
supabase --version

# Laravel動作確認
php artisan --version

# テスト環境確認
php artisan test --help
php artisan dusk --help
```

## 🎯 最初のタスク（実践）

### 1. Issue作成
```markdown
タイトル: [practice] 開発環境セットアップ確認

## 概要
新規参画者として、開発環境が正しくセットアップされていることを確認する

## 要件
- [ ] 環境構築完了
- [ ] テスト実行成功
- [ ] コード品質チェック成功
- [ ] 簡単な機能追加（Hello World）

## 受け入れ条件
- [ ] 全テストが通ること
- [ ] Laravel Pintでエラーがないこと
- [ ] PHPStanでエラーがないこと
```

### 2. 実装例（Hello World）
```php
// tests/Feature/HelloWorldTest.php
<?php

use Tests\TestCase;

class HelloWorldTest extends TestCase
{
    public function test_hello_world_endpoint(): void
    {
        $response = $this->get('/api/hello');
        
        $response->assertStatus(200)
                 ->assertJson(['message' => 'Hello, World!']);
    }
}
```

```php
// routes/api.php
Route::get('/hello', function () {
    return response()->json(['message' => 'Hello, World!']);
});
```

### 3. PR作成
```markdown
## 概要
Closes #[Issue番号]
開発環境セットアップ確認のためのHello World実装

## 変更内容
- Hello World APIエンドポイント追加
- 対応するテスト追加

## テスト
- 機能テスト: HelloWorldTest
- 全テスト通過確認

## 確認事項
- [x] テストが通ること
- [x] コードスタイルが適切であること
- [x] 静的解析でエラーがないこと
```

## 🆘 よくある問題と解決方法

### 環境構築関連
| 問題 | 解決方法 |
|------|----------|
| Supabaseが起動しない | `supabase stop` → `supabase start` |
| マイグレーションエラー | `.env.local`の設定確認 |
| テストが失敗する | `php artisan config:clear` |
| Duskが動かない | ChromeDriverのインストール確認 |

### 開発プロセス関連
| 問題 | 解決方法 |
|------|----------|
| どのファイルを編集すべきか分からない | [docs/Documentation_Structure_Guide.md](./Documentation_Structure_Guide.md)を確認 |
| TDDの進め方が分からない | [docs/TDD_Guide.md](./TDD_Guide.md)の実践例を参照 |
| PRの書き方が分からない | [docs/Git_PR_Guidelines.md](./Git_PR_Guidelines.md)のテンプレート使用 |

## 📞 サポート・質問

### 情報が見つからない場合
1. **[AGENTS.md](../AGENTS.md)** の目次から探す
2. **[docs/Documentation_Structure_Guide.md](./Documentation_Structure_Guide.md)** のマップを確認
3. **GitHub Issues** で質問を作成

### 改善提案
ドキュメントの改善提案は、以下のテンプレートでIssueを作成：

```markdown
タイトル: [docs] クイックスタートガイド改善提案

## 現在の問題
新規参画時に困ったこと

## 提案内容
どのように改善したいか

## 期待効果
改善により得られる効果
```

## 🎉 次のステップ

環境構築と最初のタスクが完了したら：

1. **実際の機能開発**: GitHub Issuesから適切なタスクを選択
2. **チーム連携**: 他の開発者・AIエージェントとの協働
3. **継続学習**: より高度な技術仕様の理解
4. **貢献拡大**: ドキュメント改善やプロセス最適化への参加

---

> 🚀 **Welcome to JTT-Apps!** このガイドに従って、効率的に開発を開始してください。不明な点があれば、遠慮なくIssueで質問してください。
