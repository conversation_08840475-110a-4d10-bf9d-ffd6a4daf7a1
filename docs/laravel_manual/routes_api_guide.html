<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ルートとAPI設計ガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        .success {
            background-color: #ecfdf5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--success);
        }

        .danger {
            background-color: #fef2f2;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--danger);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .comparison-title.good {
            color: var(--success);
        }

        .comparison-title.bad {
            color: var(--danger);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">ルートとAPI設計ガイド</span>
        </div>

        <h1 class="title">ルートとAPI設計ガイド</h1>
        <p class="subtitle">WebアプリケーションとAPIの効率的なルート設計と実装方法</p>

        <section id="overview" class="section">
            <h2 class="section-title">ルート設計の基本原則</h2>
            <p>
                ルート設計は、アプリケーションのURL構造を決定する重要な要素です。適切に設計されたルートは、
                直感的でわかりやすく、SEOにも優れています。
            </p>

            <h3>一貫性のあるルート命名</h3>
            <p>
                ルートには一貫性のある命名規則を適用し、直感的に理解できるようにします。
                JTTアプリでは以下の命名規則を採用しています：
            </p>
            <ul>
                <li>複数形のリソース名を使用（users, staff_shifts）</li>
                <li>snake_caseでURLパスを構成</li>
                <li>RESTfulなアクション名を使用（index, show, store, update, destroy）</li>
                <li>ネストされたリソースは親子関係を明示（users/{user}/shifts）</li>
            </ul>

            <h3>基本的なルート定義</h3>
            <p>
                Laravelでは、<code>routes/web.php</code>と<code>routes/api.php</code>にルートを定義します。
                Webアプリケーションのルートは<code>web.php</code>に、API用のルートは<code>api.php</code>に記述します。
            </p>

            <pre><code>// routes/web.php
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth'])
    ->name('dashboard');

// routes/api.php
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('staff-shifts', StaffShiftController::class);
});</code></pre>

            <div class="highlight">
                <p><strong>ルート設計のポイント：</strong></p>
                <ul>
                    <li>URLは小文字、ハイフン区切りで簡潔に</li>
                    <li>HTTPメソッドを適切に使い分ける（GET, POST, PUT, DELETE）</li>
                    <li>ルート名は一貫性を持たせ、名前付きルートを活用する</li>
                    <li>URLパラメータは必要最小限に抑える</li>
                </ul>
            </div>
        </section>

        <section id="restful-api" class="section">
            <h2 class="section-title">RESTful API設計</h2>
            
            <p>
                RESTfulなAPI設計は、予測可能で一貫性のあるエンドポイントを提供し、クライアント側の開発を
                容易にします。JTTアプリでは、RESTの原則に従ったAPI設計を推奨します。
            </p>

            <h3>RESTful リソースコントローラー</h3>
            <p>
                Laravelの<code>apiResource</code>メソッドを使用して、標準的なRESTfulルートを簡単に定義できます。
            </p>

            <pre><code>// routes/api.php
Route::apiResource('staff-shifts', StaffShiftController::class);</code></pre>

            <p>上記は以下の7つのルートを生成します：</p>
            <table>
                <thead>
                    <tr>
                        <th>HTTPメソッド</th>
                        <th>URL</th>
                        <th>アクション</th>
                        <th>目的</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>GET</td>
                        <td>/api/staff-shifts</td>
                        <td>index</td>
                        <td>リソース一覧の取得</td>
                    </tr>
                    <tr>
                        <td>GET</td>
                        <td>/api/staff-shifts/{id}</td>
                        <td>show</td>
                        <td>特定リソースの取得</td>
                    </tr>
                    <tr>
                        <td>POST</td>
                        <td>/api/staff-shifts</td>
                        <td>store</td>
                        <td>新規リソースの作成</td>
                    </tr>
                    <tr>
                        <td>PUT/PATCH</td>
                        <td>/api/staff-shifts/{id}</td>
                        <td>update</td>
                        <td>既存リソースの更新</td>
                    </tr>
                    <tr>
                        <td>DELETE</td>
                        <td>/api/staff-shifts/{id}</td>
                        <td>destroy</td>
                        <td>リソースの削除</td>
                    </tr>
                </tbody>
            </table>

            <h3>APIリソースでのレスポンス整形</h3>
            <p>
                一貫したレスポンス形式を提供するために、Laravelの<code>Resource</code>クラスを活用します。
            </p>

            <pre><code>// app/Http/Resources/StaffShiftResource.php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StaffShiftResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'staff' => [
                'id' => $this->staff->id,
                'name' => $this->staff->name,
            ],
            'start_time' => $this->start_time->toIso8601String(),
            'end_time' => $this->end_time->toIso8601String(),
            'role' => $this->role_col,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</code></pre>

            <h3>コントローラーでのAPIリソース使用例</h3>
            <pre><code>// StaffShiftController.php
public function index()
{
    $shifts = StaffShift::with('staff')->paginate();
    return StaffShiftResource::collection($shifts);
}

public function show(StaffShift $staffShift)
{
    return new StaffShiftResource($staffShift);
}</code></pre>

            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title good">良いAPI設計</div>
                    <ul>
                        <li>リソース志向の命名（名詞の複数形）</li>
                        <li>一貫したHTTPメソッド使用</li>
                        <li>適切なHTTPステータスコード</li>
                        <li>一貫したレスポンス形式</li>
                        <li>バージョニングの導入</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title bad">悪いAPI設計</div>
                    <ul>
                        <li>動詞を含むエンドポイント（/getAllUsers）</li>
                        <li>GETでデータ更新、POSTでデータ取得など</li>
                        <li>常に200を返すなど不適切なステータスコード</li>
                        <li>エンドポイントごとに異なるレスポンス構造</li>
                        <li>バージョン管理の欠如</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="route-groups" class="section">
            <h2 class="section-title">ルートグループの活用</h2>
            
            <p>
                ルートグループを使用すると、共通の属性（ミドルウェア、プレフィックス、名前空間など）を
                持つルートをまとめて定義できます。これにより、コードの重複を減らし、可読性を向上させます。
            </p>

            <h3>ミドルウェアグループ</h3>
            <pre><code>// 認証が必要なルートグループ
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('staff-shifts', StaffShiftController::class);
    Route::apiResource('notifications', NotificationController::class);
});</code></pre>

            <h3>プレフィックスとエンドポイントのグループ化</h3>
            <pre><code>// 管理者用APIエンドポイント
Route::prefix('admin')->middleware(['auth:sanctum', 'can:access-admin'])->group(function () {
    Route::apiResource('users', Admin\UserController::class);
    Route::apiResource('settings', Admin\SettingController::class);
});</code></pre>

            <h3>ネストされたリソースルート</h3>
            <p>親子関係にあるリソースは、ネストされたルートで表現することができます。</p>
            <pre><code>// ユーザーとそのシフトの関係
Route::apiResource('users.shifts', UserShiftController::class);</code></pre>

            <p>ただし、深いネストは避け、1階層のネストにとどめることを推奨します。上記のルートは以下のようになります：</p>
            <ul>
                <li>GET /api/users/{user}/shifts</li>
                <li>GET /api/users/{user}/shifts/{shift}</li>
                <li>POST /api/users/{user}/shifts</li>
                <li>PUT /api/users/{user}/shifts/{shift}</li>
                <li>DELETE /api/users/{user}/shifts/{shift}</li>
            </ul>

            <div class="highlight">
                <p><strong>Tips：</strong></p>
                <p>ネストが複雑になりすぎる場合は、代わりにクエリパラメータを使用することを検討してください。例えば、<code>GET /api/shifts?user_id=123</code>のようにします。</p>
            </div>
        </section>

        <section id="api-authentication" class="section">
            <h2 class="section-title">API認証（Sanctum）</h2>
            
            <p>
                JTTアプリでは、APIの認証にLaravelのSanctumを使用しています。Sanctumは、
                APIトークン認証とSPA（シングルページアプリケーション）のためのシンプルな認証システムを提供します。
            </p>

            <h3>Sanctumのセットアップ</h3>
            <p>
                Sanctumはデフォルトでインストールされていますが、設定が必要です。
            </p>
            <pre><code>// config/sanctum.php の主要な設定
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    env('APP_URL') ? ','.parse_url(env('APP_URL'), PHP_URL_HOST) : ''
))),

'expiration' => 60 * 24, // トークンの有効期限（分）</code></pre>

            <h3>APIトークン認証</h3>
            <p>
                モバイルアプリやサードパーティアプリケーションのためのAPIトークン認証の実装例：
            </p>
            <pre><code>// LoginController.php
public function login(Request $request)
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    $user = User::where('email', $request->email)->first();

    if (! $user || ! Hash::check($request->password, $user->password)) {
        throw ValidationException::withMessages([
            'email' => ['認証情報が正しくありません。'],
        ]);
    }

    // 既存のトークンを削除（オプション）
    $user->tokens()->delete();

    // 新しいトークンを生成
    $token = $user->createToken('api-token')->plainTextToken;

    return response()->json([
        'token' => $token,
        'user' => new UserResource($user),
    ]);
}</code></pre>

            <h3>SPA認証</h3>
            <p>
                SPAのためのCookie認証を設定することで、より安全にAPIを利用できます。
            </p>
            <pre><code>// config/cors.php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'supports_credentials' => true,</code></pre>

            <pre><code>// SPAのログインプロセス（フロントエンド側）
// 1. CSRFトークンの取得
axios.get('/sanctum/csrf-cookie')
  .then(response => {
    // 2. ログインリクエスト
    axios.post('/api/login', {
      email: '<EMAIL>',
      password: 'password'
    })
    .then(response => {
      // 3. 認証完了
      // 以降のリクエストはCookieが自動的に送信される
    });
});</code></pre>

            <div class="warning">
                <p><strong>セキュリティ注意点：</strong></p>
                <p>実際の環境では、以下のセキュリティ対策も検討してください：</p>
                <ul>
                    <li>トークンの有効期限を適切に設定する</li>
                    <li>トークンの使用範囲を制限する（スコープ機能）</li>
                    <li>HTTPSを強制する</li>
                    <li>CORS設定を適切に行い、許可するオリジンを限定する</li>
                </ul>
            </div>
        </section>

        <section id="response-formats" class="section">
            <h2 class="section-title">レスポンス形式の標準化</h2>
            
            <p>
                一貫性のあるAPIレスポンス形式を提供することで、クライアント側の開発が容易になります。
                JTTアプリでは、以下のレスポンス形式を標準としています。
            </p>

            <h3>成功レスポンスの形式</h3>
            <pre><code>{
  "data": {
    // リソースのデータ
  },
  "meta": {
    // ページネーション情報など
  },
  "links": {
    // HATEOAS リンク
  }
}</code></pre>

            <h3>エラーレスポンスの形式</h3>
            <pre><code>{
  "message": "エラーの概要メッセージ",
  "errors": {
    "field1": [
      "エラーメッセージ1",
      "エラーメッセージ2"
    ],
    "field2": [
      "エラーメッセージ"
    ]
  }
}</code></pre>

            <h3>レスポンストランスフォーマーの実装</h3>
            <p>
                一貫したレスポンス形式を自動的に適用するためのレスポンストランスフォーマーの例：
            </p>
            <pre><code>// app/Http/Responses/ApiResponse.php
namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;

class ApiResponse implements Responsable
{
    protected $data;
    protected $meta;
    protected $status;
    protected $headers;

    public function __construct($data = null, $meta = [], $status = 200, array $headers = [])
    {
        $this->data = $data;
        $this->meta = $meta;
        $this->status = $status;
        $this->headers = $headers;
    }

    public function toResponse($request)
    {
        $response = [
            'data' => $this->data,
        ];

        if (!empty($this->meta)) {
            $response['meta'] = $this->meta;
        }

        return new JsonResponse($response, $this->status, $this->headers);
    }
}</code></pre>

            <h3>エラーハンドリングミドルウェア</h3>
            <p>
                APIのエラーレスポンスを統一するためのミドルウェアの例：
            </p>
            <pre><code>// app/Exceptions/Handler.php
public function render($request, Throwable $e)
{
    if ($request->expectsJson()) {
        if ($e instanceof ValidationException) {
            return response()->json([
                'message' => 'バリデーションエラーが発生しました。',
                'errors' => $e->errors(),
            ], 422);
        }

        if ($e instanceof AuthenticationException) {
            return response()->json([
                'message' => '認証エラーが発生しました。',
            ], 401);
        }

        // その他の例外処理...
    }

    return parent::render($request, $e);
}</code></pre>

            <div class="highlight">
                <p><strong>レスポンス形式のベストプラクティス：</strong></p>
                <ul>
                    <li>一貫した構造を維持する</li>
                    <li>適切なHTTPステータスコードを使用する</li>
                    <li>エラーメッセージは明確かつ具体的に</li>
                    <li>ページネーション、ソート、フィルタリング情報はmetaに含める</li>
                    <li>HATEOAS原則に従い、関連リソースへのリンクを提供する</li>
                </ul>
            </div>
        </section>

        <section id="api-versioning" class="section">
            <h2 class="section-title">APIバージョニング戦略</h2>
            
            <p>
                APIの変更が既存のクライアントに影響を与えないように、適切なバージョニング戦略を
                導入することが重要です。JTTアプリでは、URLベースのバージョニングを採用しています。
            </p>

            <h3>URLベースのバージョニング</h3>
            <p>
                URLパスにバージョン番号を含める方法です。シンプルで直感的です。
            </p>
            <pre><code>// routes/api.php
Route::prefix('v1')->group(function () {
    Route::apiResource('staff-shifts', Api\V1\StaffShiftController::class);
});

Route::prefix('v2')->group(function () {
    Route::apiResource('staff-shifts', Api\V2\StaffShiftController::class);
});</code></pre>

            <h3>コントローラーの構造</h3>
            <p>
                バージョン別にコントローラーを分離し、継承や特性（traits）を使って共通コードを共有します。
            </p>
            <pre><code>// app/Http/Controllers/Api/V1/StaffShiftController.php
namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\V1\StaffShiftResource;

class StaffShiftController extends Controller
{
    // V1固有の実装
}

// app/Http/Controllers/Api/V2/StaffShiftController.php
namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\V2\StaffShiftResource;

class StaffShiftController extends Controller
{
    // V2固有の実装
}</code></pre>

            <h3>リソースの分離</h3>
            <p>
                APIリソースもバージョン別に分離し、各バージョンで異なるフィールドや変換ロジックを提供します。
            </p>
            <pre><code>// app/Http/Resources/V1/StaffShiftResource.php
namespace App\Http\Resources\V1;

use Illuminate\Http\Resources\Json\JsonResource;

class StaffShiftResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'staff_id' => $this->staff_id,
            'staff_name' => $this->staff->name,
            'start_time' => $this->start_time->toIso8601String(),
            'end_time' => $this->end_time->toIso8601String(),
            'role' => $this->role_col,
        ];
    }
}

// app/Http/Resources/V2/StaffShiftResource.php
namespace App\Http\Resources\V2;

use Illuminate\Http\Resources\Json\JsonResource;

class StaffShiftResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'staff' => [
                'id' => $this->staff_id,
                'name' => $this->staff->name,
                'email' => $this->staff->email,
            ],
            'schedule' => [
                'starts_at' => $this->start_time->toIso8601String(),
                'ends_at' => $this->end_time->toIso8601String(),
                'duration_minutes' => $this->start_time->diffInMinutes($this->end_time),
            ],
            'role' => $this->role_col,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}</code></pre>

            <div class="warning">
                <p><strong>バージョニングに関する注意点：</strong></p>
                <ul>
                    <li>APIのメジャーバージョンは互換性のない変更がある場合のみ更新する</li>
                    <li>古いバージョンのAPIは一定期間サポートし、移行期間を設ける</li>
                    <li>APIドキュメントには各バージョンの違いと非推奨情報を明記する</li>
                    <li>新しいバージョンへの移行を促すメカニズム（レスポンスヘッダーなど）を検討する</li>
                </ul>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>