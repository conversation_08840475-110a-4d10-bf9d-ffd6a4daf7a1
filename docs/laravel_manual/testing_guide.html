<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>テスト戦略ガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        .success {
            background-color: #ecfdf5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--success);
        }

        .danger {
            background-color: #fef2f2;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--danger);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .comparison-title.good {
            color: var(--success);
        }

        .comparison-title.bad {
            color: var(--danger);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">テスト戦略ガイド</span>
        </div>

        <h1 class="title">テスト戦略ガイド</h1>
        <p class="subtitle">効率的なテスト戦略と実装方法について解説します</p>

        <section id="overview" class="section">
            <h2 class="section-title">テスト戦略の基本</h2>
            <p>
                効果的なテストは高品質なソフトウェア開発の鍵です。JTTアプリでは、複数の層でのテストを
                組み合わせて、機能の信頼性と品質を確保しています。
            </p>

            <h3>テストピラミッド</h3>
            <p>
                テストピラミッドの概念に基づき、以下の構成でテストを設計しています：
            </p>
            <ul>
                <li><strong>単体テスト（Unit Tests）</strong>：個々のクラスやメソッドの機能をテスト</li>
                <li><strong>統合テスト（Integration Tests）</strong>：複数のコンポーネントの連携をテスト</li>
                <li><strong>機能テスト（Feature Tests）</strong>：特定の機能やユースケースをテスト</li>
                <li><strong>E2Eテスト（End-to-End Tests）</strong>：ユーザーの視点からシステム全体をテスト</li>
            </ul>

            <div class="highlight">
                <p><strong>テストの基本原則：</strong></p>
                <ul>
                    <li>単体テストは多く、上位のテストは少なめに設計する</li>
                    <li>テストは独立して実行でき、順序に依存しないようにする</li>
                    <li>テストは高速に実行できるようにする</li>
                    <li>テストはドキュメントの役割も果たす</li>
                    <li>テストはコード変更時の安全網となる</li>
                </ul>
            </div>
        </section>

        <section id="unit-tests" class="section">
            <h2 class="section-title">単体テスト（Pest）</h2>
            
            <p>
                JTTアプリでは、単体テストにPestテストフレームワークを使用しています。
                Pestは、PHPUnitをベースに、より簡潔で読みやすい構文を提供するフレームワークです。
            </p>

            <h3>Pestの基本構文</h3>
            <pre><code>test('sum calculates correctly', function () {
    $calculator = new Calculator();
    expect($calculator->sum(2, 3))->toBe(5);
});

it('can sum two numbers', function () {
    $calculator = new Calculator();
    expect($calculator->sum(2, 3))->toBe(5);
});</code></pre>

            <h3>グループ化とセットアップ</h3>
            <pre><code>beforeEach(function () {
    $this->service = new StaffShiftService();
});

describe('StaffShiftService', function () {
    it('creates a new shift', function () {
        $shift = $this->service->createShift([
            'staff_id' => 1,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
            'role_col' => 'cashier',
        ]);
        
        expect($shift)->toBeInstanceOf(StaffShift::class);
        expect($shift->role_col)->toBe('cashier');
    });
    
    it('throws exception for overlapping shifts', function () {
        // 既存のシフトを作成
        // ...
        
        // 重複するシフトを作成しようとすると例外が発生するはず
        expect(fn () => $this->service->createShift([
            'staff_id' => 1,
            'start_time' => '2025-01-01 10:00:00',
            'end_time' => '2025-01-01 16:00:00',
            'role_col' => 'cashier',
        ]))->toThrow(ShiftOverlapException::class);
    });
});</code></pre>

            <h3>モックの活用</h3>
            <p>
                外部依存を持つクラスをテストする場合、モックを利用して依存を分離します。
            </p>
            <pre><code>it('sends notification on shift creation', function () {
    // 通知サービスのモックを作成
    $notificationService = Mockery::mock(NotificationService::class);
    
    // モックの振る舞いを定義
    $notificationService->shouldReceive('sendShiftCreationNotification')
        ->once()
        ->andReturn(true);
    
    // モックを注入したサービスを作成
    $service = new StaffShiftService($notificationService);
    
    // テスト対象のメソッドを実行
    $shift = $service->createShift([/* シフトデータ */]);
    
    // 期待通りにモックが呼ばれたことはMockeryが自動的に検証
});</code></pre>

            <div class="highlight">
                <p><strong>単体テストのベストプラクティス：</strong></p>
                <ul>
                    <li>テストは1つの機能や条件のみをテストする</li>
                    <li>テスト名は「何をテストするか」を明確に表現する</li>
                    <li>AAA（Arrange-Act-Assert）パターンでテストを構造化する</li>
                    <li>境界値や異常系のテストも忘れずに作成する</li>
                    <li>依存はモックやスタブで置き換え、外部システムには依存しない</li>
                </ul>
            </div>
        </section>

        <section id="feature-tests" class="section">
            <h2 class="section-title">機能テスト</h2>
            
            <p>
                機能テストでは、HTTP層を含む一連の処理をテストし、実際のユーザーの操作を模倣します。
                LaravelのHTTPテスト機能を活用し、エンドポイントの挙動をテストします。
            </p>

            <h3>API機能テスト</h3>
            <pre><code>it('allows authenticated user to create a staff shift', function () {
    // ユーザーの認証
    $user = User::factory()->create();
    $this->actingAs($user);
    
    // リクエストの送信
    $response = $this->postJson('/api/staff-shifts', [
        'staff_id' => $user->id,
        'start_time' => '2025-01-01 09:00:00',
        'end_time' => '2025-01-01 17:00:00',
        'role_col' => 'cashier',
    ]);
    
    // レスポンスの検証
    $response->assertStatus(201)
             ->assertJsonStructure(['id', 'staff_id', 'start_time', 'end_time', 'role_col']);
    
    // データベースの検証
    $this->assertDatabaseHas('staff_shifts', [
        'staff_id' => $user->id,
        'role_col' => 'cashier',
    ]);
});</code></pre>

            <h3>Web機能テスト</h3>
            <pre><code>it('displays staff shifts on dashboard', function () {
    // ユーザーの認証
    $user = User::factory()->create();
    $this->actingAs($user);
    
    // シフトの作成
    StaffShift::factory()->count(3)->create([
        'staff_id' => $user->id,
    ]);
    
    // ダッシュボードへのリクエスト
    $response = $this->get('/dashboard');
    
    // レスポンスの検証
    $response->assertStatus(200)
             ->assertSee('シフト一覧')
             ->assertViewHas('shifts', function ($shifts) {
                 return $shifts->count() === 3;
             });
});</code></pre>

            <h3>バリデーションテスト</h3>
            <pre><code>it('validates shift input data', function () {
    // ユーザーの認証
    $user = User::factory()->create();
    $this->actingAs($user);
    
    // 不完全なデータでリクエスト
    $response = $this->postJson('/api/staff-shifts', [
        'staff_id' => $user->id,
        // start_timeが欠けている
        'end_time' => '2025-01-01 17:00:00',
        'role_col' => 'cashier',
    ]);
    
    // バリデーションエラーの検証
    $response->assertStatus(422)
             ->assertJsonValidationErrors(['start_time']);
});</code></pre>

            <div class="highlight">
                <p><strong>機能テストのベストプラクティス：</strong></p>
                <ul>
                    <li>ハッピーパスだけでなく、異常系も必ずテストする</li>
                    <li>必要に応じてファクトリーを活用してテストデータを準備する</li>
                    <li>データベースアサーションを使ってデータの永続化を確認する</li>
                    <li>認証や権限周りのテストを忘れない</li>
                    <li>テストの独立性を保つため、トランザクションを使用する</li>
                </ul>
            </div>
        </section>

        <section id="mocking" class="section">
            <h2 class="section-title">テストダブル（モック・スタブ）</h2>
            
            <p>
                外部サービスやデータベースへの依存を持つコードをテストする場合、
                テストダブルを使って依存を分離し、再現性の高いテストを作成します。
            </p>

            <h3>Mockeryの基本</h3>
            <pre><code>// サービスクラスのモック
$service = Mockery::mock(PaymentService::class);

// メソッド呼び出しの期待を設定
$service->shouldReceive('processPayment')
    ->once()  // 1回だけ呼ばれることを期待
    ->with(100, 'USD', Mockery::type('array'))  // 引数の期待
    ->andReturn(true);  // 返り値の設定

// モックの使用
$result = $service->processPayment(100, 'USD', ['card_id' => 123]);
expect($result)->toBeTrue();</code></pre>

            <h3>Laravelのモック機能</h3>
            <p>
                Laravelは様々な組み込みサービスに対するモック機能を提供しています。
            </p>
            <pre><code>// HTTPリクエストのモック
Http::fake([
    'https://api.example.com/*' => Http::response(['success' => true], 200),
]);

// キューのモック
Queue::fake();

// イベントのモック
Event::fake();

// テスト後の検証
Queue::assertPushed(ProcessPaymentJob::class, function ($job) {
    return $job->amount === 100;
});

Event::assertDispatched(PaymentProcessed::class);</code></pre>

            <h3>メール送信のテスト</h3>
            <pre><code>it('sends welcome email after registration', function () {
    // メールのフェイク
    Mail::fake();
    
    // 登録処理
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);
    
    // リダイレクト確認
    $response->assertRedirect('/dashboard');
    
    // メール送信の検証
    Mail::assertSent(WelcomeEmail::class, function ($mail) {
        return $mail->hasTo('<EMAIL>');
    });
});</code></pre>

            <div class="warning">
                <p><strong>モックの注意点：</strong></p>
                <p>モックの過剰な使用は、実際のコードとのかい離を生む可能性があります。プロダクションのような環境でのテストも併用し、バランスを保つことが重要です。</p>
            </div>
        </section>

        <section id="database-tests" class="section">
            <h2 class="section-title">データベーステスト</h2>
            
            <p>
                データベースを使用するテストでは、テスト用のデータベース設定や
                トランザクションを活用して、テストの独立性と再現性を確保します。
            </p>

            <h3>テストデータベースの設定</h3>
            <p>
                <code>phpunit.xml</code>または<code>.env.testing</code>ファイルでテスト用のデータベース接続を設定します。
            </p>
            <pre><code>&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;phpunit ...&gt;
    &lt;php&gt;
        &lt;env name="APP_ENV" value="testing"/&gt;
        &lt;env name="DB_CONNECTION" value="sqlite"/&gt;
        &lt;env name="DB_DATABASE" value=":memory:"/&gt;
        ...
    &lt;/php&gt;
&lt;/phpunit&gt;</code></pre>

            <h3>トランザクションを使ったテスト</h3>
            <p>
                <code>RefreshDatabase</code>や<code>DatabaseTransactions</code>トレイトを使用して、
                テスト間でのデータの独立性を確保します。
            </p>
            <pre><code>uses(RefreshDatabase::class);

it('creates a staff shift record', function () {
    // テストデータベースにシフトを作成
    $shift = StaffShift::create([
        'staff_id' => 1,
        'start_time' => '2025-01-01 09:00:00',
        'end_time' => '2025-01-01 17:00:00',
        'role_col' => 'cashier',
    ]);
    
    // データベースに正しく保存されたか確認
    $this->assertDatabaseHas('staff_shifts', [
        'id' => $shift->id,
        'role_col' => 'cashier',
    ]);
});</code></pre>

            <h3>ファクトリーの活用</h3>
            <p>
                モデルファクトリーを使用して、テストデータを効率的に生成します。
            </p>
            <pre><code>// ファクトリーの定義（UserFactory.php）
public function definition()
{
    return [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => Hash::make('password'),
    ];
}

// ファクトリーの使用
$user = User::factory()->create();
$shifts = StaffShift::factory()->count(3)->create([
    'staff_id' => $user->id,
]);</code></pre>

            <div class="highlight">
                <p><strong>データベーステストのベストプラクティス：</strong></p>
                <ul>
                    <li>テスト用のデータベースを本番と分離する</li>
                    <li>トランザクションを使ってテスト間の独立性を確保する</li>
                    <li>ファクトリーを活用してテストデータを生成する</li>
                    <li>データベースアサーションで検証する</li>
                    <li>必要に応じてシーダーでテストデータを準備する</li>
                </ul>
            </div>
        </section>

        <section id="e2e-tests" class="section">
            <h2 class="section-title">E2Eテスト（Playwright）</h2>
            
            <p>
                エンドツーエンド（E2E）テストでは、実際のブラウザでユーザーの操作を模倣し、
                アプリケーション全体の動作を検証します。JTTアプリでは、Playwrightを使用してE2Eテストを行っています。
            </p>

            <h3>Playwrightのセットアップ</h3>
            <pre><code>// インストール
npm install --save-dev @playwright/test

// テストの実行
npx playwright test</code></pre>

            <h3>基本的なE2Eテスト</h3>
            <pre><code>import { test, expect } from '@playwright/test';

test('user can login and see dashboard', async ({ page }) => {
  // ログインページにアクセス
  await page.goto('http://localhost:8000/login');
  
  // フォームに入力
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'password');
  
  // ログインボタンをクリック
  await page.click('button[type="submit"]');
  
  // ダッシュボードにリダイレクトされることを確認
  await expect(page).toHaveURL('http://localhost:8000/dashboard');
  
  // ダッシュボードの要素が表示されることを確認
  await expect(page.locator('h1')).toContainText('Dashboard');
});</code></pre>

            <h3>複雑なユーザーシナリオのテスト</h3>
            <pre><code>test('user can create a staff shift', async ({ page }) => {
  // ログイン処理
  await page.goto('http://localhost:8000/login');
  // ... ログイン処理 ...
  
  // シフト作成ページにアクセス
  await page.click('text=Create Shift');
  
  // フォームに入力
  await page.fill('input[name="start_time"]', '2025-01-01T09:00');
  await page.fill('input[name="end_time"]', '2025-01-01T17:00');
  await page.selectOption('select[name="role_col"]', 'cashier');
  
  // 作成ボタンをクリック
  await page.click('button[type="submit"]');
  
  // 成功メッセージを確認
  await expect(page.locator('.alert-success')).toContainText('Shift created');
  
  // シフト一覧に表示されることを確認
  await expect(page.locator('table')).toContainText('2025-01-01');
  await expect(page.locator('table')).toContainText('09:00');
});</code></pre>

            <h3>API E2Eテスト</h3>
            <pre><code>import { test, expect } from '@playwright/test';

test('API endpoint returns staff shifts', async ({ request }) => {
  // ログイントークンの取得
  const loginResponse = await request.post('http://localhost:8000/api/login', {
    data: {
      email: '<EMAIL>',
      password: 'password',
    }
  });
  
  const { token } = await loginResponse.json();
  
  // APIエンドポイントの呼び出し
  const shiftsResponse = await request.get('http://localhost:8000/api/staff-shifts', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  // レスポンスの検証
  expect(shiftsResponse.status()).toBe(200);
  
  const data = await shiftsResponse.json();
  expect(data.data.length).toBeGreaterThan(0);
});</code></pre>

            <div class="warning">
                <p><strong>E2Eテストの注意点：</strong></p>
                <p>E2Eテストは実行に時間がかかり、環境依存性も高いため、必要な箇所に集中して作成します。全ての機能をE2Eテストでカバーするのではなく、重要なユーザーフローに焦点を当てましょう。</p>
            </div>
        </section>

        <section id="ci-cd" class="section">
            <h2 class="section-title">CI/CDパイプラインとの連携</h2>
            
            <p>
                テストはCI/CDパイプラインと連携して自動実行することで、継続的な品質保証を実現します。
                JTTアプリでは、GitHub Actionsを使用してテストを自動化しています。
            </p>

            <h3>GitHub Actionsの設定例</h3>
            <pre><code># .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  laravel-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: testing
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: pdo, pgsql, pdo_pgsql
        coverage: pcov
    
    - name: Copy .env
      run: cp .env.example .env.testing
    
    - name: Install Dependencies
      run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress
    
    - name: Generate key
      run: php artisan key:generate --env=testing
    
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
    
    - name: Run Migrations
      run: php artisan migrate --env=testing
    
    - name: Run PHPUnit Tests
      run: vendor/bin/pest --coverage
    
    - name: Run Playwright Tests
      run: |
        php artisan serve --env=testing &
        npm install
        npx playwright install --with-deps
        npx playwright test
      env:
        CI: true</code></pre>

            <h3>テストカバレッジの最適化</h3>
            <p>
                テストカバレッジを測定し、重要な部分のテストが不足していないか確認します。
            </p>
            <pre><code># PHPUnitでカバレッジレポートを生成
vendor/bin/pest --coverage-html reports/

# カバレッジ閾値の設定
vendor/bin/pest --coverage-min=80</code></pre>

            <div class="highlight">
                <p><strong>CI/CDのベストプラクティス：</strong></p>
                <ul>
                    <li>マージ前に全てのテストが通ることを確認する</li>
                    <li>テスト環境を本番に近づけ、環境差異による問題を減らす</li>
                    <li>テスト実行を高速化するため、並列実行や分割実行を検討する</li>
                    <li>テストカバレッジを測定し、不足している部分を特定する</li>
                    <li>失敗したテストの詳細なレポートを開発者に提供する</li>
                </ul>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>