<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>モデル設計ガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        .success {
            background-color: #ecfdf5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--success);
        }

        .danger {
            background-color: #fef2f2;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--danger);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .comparison-title.good {
            color: var(--success);
        }

        .comparison-title.bad {
            color: var(--danger);
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">モデル設計ガイド</span>
        </div>

        <h1 class="title">モデル設計ガイド</h1>
        <p class="subtitle">Eloquentモデルの効率的な実装とリレーションシップの設計について解説します</p>

        <section id="overview" class="section">
            <h2 class="section-title">Eloquentモデルの基本</h2>
            <p>
                Eloquentモデルは、データベーステーブルの行を表すオブジェクトであり、データベースとのやりとりを
                抽象化します。適切に設計されたモデルは、可読性、保守性、再利用性を向上させます。
            </p>

            <h3>基本的な命名規則</h3>
            <ul>
                <li><strong>モデル名</strong>：単数形・PascalCase（例：<code>StaffShift</code>）</li>
                <li><strong>テーブル名</strong>：複数形・snake_case（例：<code>staff_shifts</code>）</li>
                <li><strong>主キー</strong>：デフォルトは<code>id</code>（変更可能）</li>
                <li><strong>外部キー</strong>：単数形モデル名_id（例：<code>user_id</code>）</li>
                <li><strong>タイムスタンプ</strong>：<code>created_at</code>、<code>updated_at</code></li>
            </ul>

            <h3>基本的なモデル作成</h3>
            <pre><code>php artisan make:model StaffShift -m</code></pre>
            <p>
                <code>-m</code>オプションで、モデルと一緒にマイグレーションファイルも生成されます。
                <code>-c</code>、<code>-f</code>、<code>-s</code>オプションを追加すると、コントローラー、ファクトリー、シーダーも同時に生成できます。
            </p>

            <h3>モデルの基本構造</h3>
            <pre><code><?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffShift extends Model
{
    use HasFactory;
    
    // テーブル名を明示的に指定（必要な場合のみ）
    protected $table = 'staff_shifts';
    
    // マスアサインメント対象の属性
    protected $fillable = [
        'staff_id',
        'start_time',
        'end_time',
        'role_col',
    ];
    
    // 日付として扱う属性
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];
}</code></pre>

            <div class="highlight">
                <p><strong>モデル設計の重要ポイント：</strong></p>
                <ul>
                    <li>必要最小限のコードでシンプルに保つ</li>
                    <li>適切な属性のキャストを定義する</li>
                    <li>マスアサインメント保護を適切に設定する</li>
                    <li>リレーションシップを明確に定義する</li>
                </ul>
            </div>
        </section>

        <section id="fillable-guarded" class="section">
            <h2 class="section-title">マスアサインメント保護</h2>
            
            <p>
                マスアサインメント脆弱性から保護するため、<code>$fillable</code>プロパティまたは<code>$guarded</code>プロパティを
                適切に設定する必要があります。
            </p>

            <h3>$fillableと$guardedの違い</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">$fillable</div>
                    <p>一括代入を許可する属性のホワイトリスト</p>
                    <pre><code>protected $fillable = [
    'name',
    'email',
    'password',
];</code></pre>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">$guarded</div>
                    <p>一括代入を禁止する属性のブラックリスト</p>
                    <pre><code>protected $guarded = [
    'id',
    'is_admin',
];</code></pre>
                </div>
            </div>

            <div class="warning">
                <p><strong>セキュリティ上の注意：</strong></p>
                <p>JTTアプリでは、基本的に<code>$fillable</code>を使用し、明示的に許可された属性のみを一括代入可能とします。<code>$guarded = []</code>の使用は避けてください。</p>
            </div>

            <h3>一括代入の使用例</h3>
            <pre><code>// 新規作成
$shift = new StaffShift();
$shift->fill($request->validated());
$shift->save();

// または
$shift = StaffShift::create($request->validated());

// 更新
$shift->update($request->validated());</code></pre>
        </section>

        <section id="relationships" class="section">
            <h2 class="section-title">リレーションシップの設計</h2>
            
            <p>
                データベースのテーブル間のリレーションシップをEloquentモデルを通じて効率的に表現することで、
                関連データの取得や操作が容易になります。
            </p>

            <h3>主要なリレーションシップタイプ</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">一対一（hasOne/belongsTo）</div>
                    <pre><code>// User.php
public function profile()
{
    return $this->hasOne(Profile::class);
}

// Profile.php
public function user()
{
    return $this->belongsTo(User::class);
}</code></pre>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">一対多（hasMany/belongsTo）</div>
                    <pre><code>// User.php
public function staffShifts()
{
    return $this->hasMany(StaffShift::class, 'staff_id');
}

// StaffShift.php
public function staff()
{
    return $this->belongsTo(User::class, 'staff_id');
}</code></pre>
                </div>
            </div>

            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">多対多（belongsToMany）</div>
                    <pre><code>// User.php
public function roles()
{
    return $this->belongsToMany(Role::class);
}

// Role.php
public function users()
{
    return $this->belongsToMany(User::class);
}</code></pre>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">ポリモーフィック（morphTo/morphMany）</div>
                    <pre><code>// Comment.php
public function commentable()
{
    return $this->morphTo();
}

// Post.php
public function comments()
{
    return $this->morphMany(Comment::class, 'commentable');
}</code></pre>
                </div>
            </div>

            <h3>リレーションロード（Eager Loading）</h3>
            <p>
                N+1クエリ問題を回避するため、関連データをEager Loadingで取得することを推奨します。
            </p>

            <pre><code>// 推奨：Eager Loading
$shifts = StaffShift::with('staff')->get();

// 非推奨：N+1クエリ問題が発生
$shifts = StaffShift::all();
foreach ($shifts as $shift) {
    echo $shift->staff->name; // 各シフトごとにクエリが発行される
}</code></pre>

            <div class="highlight">
                <p><strong>リレーションシップのベストプラクティス：</strong></p>
                <ul>
                    <li>リレーションメソッド名は明確で直感的なものにする</li>
                    <li>必要に応じて外部キーやローカルキーを明示的に指定する</li>
                    <li>常にEager Loadingを意識し、N+1クエリ問題を防止する</li>
                    <li>複雑なリレーションは、複数の単純なリレーションに分解する</li>
                </ul>
            </div>
        </section>

        <section id="accessors-mutators" class="section">
            <h2 class="section-title">アクセサとミューテタ</h2>
            
            <p>
                アクセサとミューテタを使用すると、モデルの属性を取得または設定する際に、
                自動的に変換や加工を行うことができます。
            </p>

            <h3>アクセサ（Accessor）</h3>
            <p>
                データベースから取得した値を加工して返すメソッドです。
                メソッド命名規則は<code>get{属性名}Attribute</code>です。
            </p>

            <pre><code>// アクセサの例
public function getFullNameAttribute()
{
    return "{$this->first_name} {$this->last_name}";
}

// 使用方法
echo $user->full_name; // 自動的にアクセサが呼び出される</code></pre>

            <h3>ミューテタ（Mutator）</h3>
            <p>
                モデルに値を設定する際に自動的に加工を行うメソッドです。
                メソッド命名規則は<code>set{属性名}Attribute</code>です。
            </p>

            <pre><code>// ミューテタの例
public function setPasswordAttribute($value)
{
    $this->attributes['password'] = bcrypt($value);
}

// 使用方法
$user->password = 'plain-text'; // 自動的にハッシュ化される</code></pre>

            <h3>Laravel 8以降での新しい構文</h3>
            <p>
                Laravel 8以降では、よりシンプルなアクセサ・ミューテタ構文も使用できます。
            </p>

            <pre><code>// Laravel 8以降の構文
use Illuminate\Database\Eloquent\Casts\Attribute;

// アクセサとミューテタを一つのメソッドにまとめる
protected function fullName(): Attribute
{
    return Attribute::make(
        get: fn () => "{$this->first_name} {$this->last_name}",
        set: fn ($value) => [
            'first_name' => strtok($value, ' '),
            'last_name' => substr($value, strlen(strtok($value, ' ')) + 1)
        ],
    );
}</code></pre>

            <div class="highlight">
                <p><strong>Tips:</strong></p>
                <p>アクセサとミューテタを使うと、ビジネスロジックをモデルにカプセル化でき、コントローラーやビューをシンプルに保てます。ただし、複雑なロジックはサービスクラスに移動させましょう。</p>
            </div>
        </section>

        <section id="scopes" class="section">
            <h2 class="section-title">クエリスコープ</h2>
            
            <p>
                クエリスコープを使用すると、頻繁に使用するクエリ条件をカプセル化し、再利用可能にできます。
                コード全体の一貫性と可読性が向上します。
            </p>

            <h3>ローカルスコープ</h3>
            <p>
                特定のモデルに対して定義されるスコープです。
                メソッド名は<code>scope{名前}</code>の形式で定義します。
            </p>

            <pre><code>// StaffShift.php内のローカルスコープ
public function scopeActive($query)
{
    return $query->where('status', 'active');
}

public function scopeForDate($query, $date)
{
    return $query->whereDate('start_time', '<=', $date)
                 ->whereDate('end_time', '>=', $date);
}

// 使用方法
$activeShifts = StaffShift::active()->get();
$todayShifts = StaffShift::forDate(now())->get();</code></pre>

            <h3>グローバルスコープ</h3>
            <p>
                特定のモデルの全クエリに自動的に適用されるスコープです。
                「論理削除」などの機能を実装する際に便利です。
            </p>

            <pre><code>// グローバルスコープの定義
namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class ActiveScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('is_active', true);
    }
}

// モデルでのグローバルスコープの適用
protected static function booted()
{
    static::addGlobalScope(new ActiveScope);
}</code></pre>

            <div class="highlight">
                <p><strong>スコープのベストプラクティス：</strong></p>
                <ul>
                    <li>頻繁に使用するクエリ条件はスコープ化する</li>
                    <li>スコープ名は動詞または形容詞で始め、その機能を明確に表す</li>
                    <li>複数のスコープを組み合わせて使用できるよう設計する</li>
                    <li>必要に応じてスコープをチェーンする（<code>StaffShift::active()->forDate(now())->get()</code>）</li>
                </ul>
            </div>
        </section>

        <section id="fat-model" class="section">
            <h2 class="section-title">モデルの肥大化防止</h2>
            
            <p>
                「ファットモデル、スキニーコントローラー」の原則は重要ですが、モデルが肥大化しすぎると
                保守が困難になります。適切な責務分離を行い、モデルの肥大化を防ぎましょう。
            </p>

            <h3>モデルの肥大化を防ぐ方法</h3>
            <ol>
                <li><strong>サービスクラスの活用</strong>：複雑なビジネスロジックをサービスクラスに移動する</li>
                <li><strong>トレイトの活用</strong>：共通機能をトレイトとして抽出する</li>
                <li><strong>Actions/Jobs</strong>：特定の操作を専用のクラスに分離する</li>
                <li><strong>観察者（Observer）パターン</strong>：イベント処理をObserverに分離する</li>
            </ol>

            <h3>トレイトの活用例</h3>
            <pre><code>// app/Traits/HasUserPermissions.php
namespace App\Traits;

trait HasUserPermissions
{
    public function hasPermission($permission)
    {
        return $this->permissions->contains('name', $permission);
    }
    
    public function grantPermission($permission)
    {
        // 権限付与の実装
    }
    
    public function revokePermission($permission)
    {
        // 権限剥奪の実装
    }
}

// Userモデルでの使用
use App\Traits\HasUserPermissions;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasUserPermissions;
    
    // ...
}</code></pre>

            <h3>Observerパターンの活用例</h3>
            <pre><code>// app/Observers/StaffShiftObserver.php
namespace App\Observers;

use App\Models\StaffShift;

class StaffShiftObserver
{
    public function created(StaffShift $shift)
    {
        // シフト作成時の処理
    }
    
    public function updated(StaffShift $shift)
    {
        // シフト更新時の処理
    }
}

// AppServiceProvider.php
public function boot()
{
    StaffShift::observe(StaffShiftObserver::class);
}</code></pre>

            <div class="warning">
                <p><strong>注意点：</strong></p>
                <p>モデルに過剰にロジックを追加すると、テストが困難になり、責務が混在するため、適切なバランスを保つことが重要です。単一責任の原則を意識して設計しましょう。</p>
            </div>
        </section>

        <section id="transactions" class="section">
            <h2 class="section-title">トランザクション管理</h2>
            
            <p>
                複数のデータベース操作が関連する場合、トランザクションを使用して一貫性を保ちます。
                全ての操作が成功するか、全て失敗するかの「原子性」を確保します。
            </p>

            <h3>基本的なトランザクション</h3>
            <pre><code>DB::transaction(function () {
    $shift = StaffShift::create([...]);
    $notification = Notification::create([...]);
    
    // 例外が発生すると、全ての変更がロールバックされる
});</code></pre>

            <h3>手動トランザクション</h3>
            <pre><code>try {
    DB::beginTransaction();
    
    $shift = StaffShift::create([...]);
    $notification = Notification::create([...]);
    
    DB::commit();
} catch (\Exception $e) {
    DB::rollBack();
    throw $e;
}</code></pre>

            <h3>サービス層でのトランザクション管理</h3>
            <p>
                複雑なトランザクション処理は、モデルではなくサービス層で行うことを推奨します。
            </p>

            <pre><code>// StaffShiftService
public function createShiftWithNotification(array $data): StaffShift
{
    return DB::transaction(function () use ($data) {
        $shift = StaffShift::create($data);
        
        // 通知作成
        $this->notificationService->createShiftNotification($shift);
        
        // ログ記録
        $this->logService->logShiftCreation($shift);
        
        return $shift;
    });
}</code></pre>

            <div class="highlight">
                <p><strong>トランザクションのベストプラクティス：</strong></p>
                <ul>
                    <li>複数のモデルを更新する場合は常にトランザクションを使用する</li>
                    <li>トランザクション内のコードは短く保ち、必要な操作のみに制限する</li>
                    <li>長時間実行される処理や外部API呼び出しはトランザクション外で行う</li>
                    <li>例外処理を適切に行い、トランザクションの巻き戻し（rollback）を確実にする</li>
                </ul>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>