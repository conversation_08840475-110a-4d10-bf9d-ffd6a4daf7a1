<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>コントローラー・サービス設計ガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON>goe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        .success {
            background-color: #ecfdf5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--success);
        }

        .danger {
            background-color: #fef2f2;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--danger);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .comparison-title.good {
            color: var(--success);
        }

        .comparison-title.bad {
            color: var(--danger);
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">コントローラー・サービス設計ガイド</span>
        </div>

        <h1 class="title">コントローラー・サービス設計ガイド</h1>
        <p class="subtitle">コントローラーとサービス層の効率的な実装と責務分離について解説します</p>

        <section id="overview" class="section">
            <h2 class="section-title">コントローラーとサービス層の役割</h2>
            <p>
                効率的なアプリケーション設計では、コントローラーとサービス層の責務を明確に分離することが重要です。
                この分離により、コードの再利用性、テスト容易性、保守性が向上します。
            </p>

            <h3>コントローラーの責務</h3>
            <ul>
                <li><strong>HTTPリクエストの受信</strong>：ルートからのリクエスト処理</li>
                <li><strong>入力バリデーション</strong>：FormRequestクラスや基本的なバリデーション</li>
                <li><strong>サービス層の呼び出し</strong>：ビジネスロジック実行の委譲</li>
                <li><strong>レスポンスの整形</strong>：適切なHTTPレスポンスの返却</li>
                <li><strong>例外ハンドリング</strong>：エラー発生時の適切な処理</li>
            </ul>

            <h3>サービス層の責務</h3>
            <ul>
                <li><strong>ビジネスロジックの実装</strong>：ドメイン固有のルールや処理</li>
                <li><strong>トランザクション管理</strong>：データの整合性確保</li>
                <li><strong>複数モデルの操作</strong>：モデル間の連携処理</li>
                <li><strong>外部サービスとの連携</strong>：API呼び出しや他システム連携</li>
                <li><strong>イベント発行</strong>：システム内の状態変更通知</li>
            </ul>

            <div class="highlight">
                <p><strong>重要ポイント：</strong></p>
                <p>コントローラーは「何をするか」を決定し、サービス層は「どのように行うか」を実装します。この責務分離により、ビジネスロジックの再利用や効率的なテストが可能になります。</p>
            </div>
        </section>

        <section id="controller-design" class="section">
            <h2 class="section-title">コントローラー設計のベストプラクティス</h2>
            
            <h3>シンプルなコントローラー</h3>
            <p>
                コントローラーはできるだけシンプルに保ち、ビジネスロジックは全てサービス層に委譲します。
                理想的なコントローラーメソッドは10行程度の短いコードになるはずです。
            </p>

            <h3>リソースコントローラーの活用</h3>
            <p>
                標準的なCRUD操作には、Laravelのリソースコントローラーを活用します。これにより、一貫性のある
                エンドポイント命名と標準的な操作を提供できます。
            </p>
            <pre><code>php artisan make:controller StaffShiftController --resource</code></pre>

            <h3>FormRequestによるバリデーション</h3>
            <p>
                入力バリデーションは専用のFormRequestクラスに分離し、コントローラーをさらにシンプルにします。
            </p>
            <pre><code>php artisan make:request StoreStaffShiftRequest</code></pre>

            <h3>依存性注入の活用</h3>
            <p>
                コントローラーでは必要なサービスをコンストラクタインジェクションで注入し、
                テスト時にモックやスタブに置き換えられるようにします。
            </p>

            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title good">良い例：シンプルなコントローラー</div>
                    <pre><code><?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStaffShiftRequest;
use App\Services\StaffShiftService;
use Illuminate\Http\JsonResponse;

class StaffShiftController extends Controller
{
    public function __construct(
        private StaffShiftService $staffShiftService
    ) {
    }

    public function store(StoreStaffShiftRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $shift = $this->staffShiftService->createShift($validated);
        
        return response()->json($shift, 201);
    }
}</code></pre>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title bad">悪い例：肥大化したコントローラー</div>
                    <pre><code><?php

namespace App\Http\Controllers;

use App\Models\StaffShift;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StaffShiftController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'staff_id' => 'required|exists:users,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'role_col' => 'required|string',
        ]);

        try {
            DB::beginTransaction();
            
            // ユーザー情報を取得して権限チェック
            $user = User::findOrFail($request->staff_id);
            if (!$user->can('create-shift')) {
                return response()->json(['error' => '権限がありません'], 403);
            }
            
            // シフト重複チェック
            $existingShift = StaffShift::where('staff_id', $request->staff_id)
                ->where(function($query) use ($request) {
                    $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                        ->orWhereBetween('end_time', [$request->start_time, $request->end_time]);
                })->first();
                
            if ($existingShift) {
                return response()->json(['error' => 'シフトが重複しています'], 422);
            }
            
            // シフト作成
            $shift = new StaffShift();
            $shift->staff_id = $request->staff_id;
            $shift->start_time = $request->start_time;
            $shift->end_time = $request->end_time;
            $shift->role_col = $request->role_col;
            $shift->save();
            
            // ログ記録
            Log::info('シフトが作成されました', ['shift_id' => $shift->id]);
            
            DB::commit();
            return response()->json($shift, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('シフト作成エラー', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'シフト作成に失敗しました'], 500);
        }
    }
}</code></pre>
                </div>
            </div>
        </section>

        <section id="service-design" class="section">
            <h2 class="section-title">サービス層設計のベストプラクティス</h2>
            
            <h3>サービスクラスの役割</h3>
            <p>
                サービスクラスはビジネスロジックをカプセル化し、コントローラーから独立させることで、
                再利用性の向上とユニットテストの容易さを実現します。
            </p>

            <h3>サービスクラスの実装</h3>
            <p>
                サービスクラスは<code>app/Services</code>ディレクトリに配置し、関連するモデルやリポジトリを
                依存性注入で取得します。
            </p>

            <pre><code><?php

namespace App\Services;

use App\Models\StaffShift;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StaffShiftService
{
    public function createShift(array $data): StaffShift
    {
        return DB::transaction(function () use ($data) {
            // シフト重複チェック
            $this->checkForOverlappingShifts($data['staff_id'], $data['start_time'], $data['end_time']);
            
            // シフト作成
            $shift = new StaffShift();
            $shift->fill($data);
            $shift->save();
            
            // 関連処理（通知送信やイベント発行など）
            $this->notifyShiftCreated($shift);
            
            return $shift;
        });
    }
    
    private function checkForOverlappingShifts(int $staffId, string $startTime, string $endTime): void
    {
        $existingShift = StaffShift::where('staff_id', $staffId)
            ->where(function($query) use ($startTime, $endTime) {
                $query->whereBetween('start_time', [$startTime, $endTime])
                    ->orWhereBetween('end_time', [$startTime, $endTime]);
            })->first();
            
        if ($existingShift) {
            throw new \Exception('シフトが重複しています');
        }
    }
    
    private function notifyShiftCreated(StaffShift $shift): void
    {
        // 通知やイベント発行のロジック
        Log::info('シフトが作成されました', ['shift_id' => $shift->id]);
    }
}</code></pre>

            <h3>サービスの責務分割</h3>
            <p>
                サービスが大きくなりすぎる場合は、より小さな単位に分割します。例えば、通知機能を別のサービスに分離したり、
                特定のビジネスロジックを専用のクラスに抽出したりします。
            </p>

            <div class="highlight">
                <p><strong>サービス設計のポイント：</strong></p>
                <ul>
                    <li><strong>単一責任の原則</strong>：各サービスは明確に定義された1つの責任を持つべき</li>
                    <li><strong>依存性の明示</strong>：依存するサービスやリポジトリを明示的にインジェクション</li>
                    <li><strong>再利用可能なAPI</strong>：メソッドは再利用可能で明確な入出力を持つべき</li>
                    <li><strong>適切な例外処理</strong>：ドメイン固有の例外を適切に投げるべき</li>
                </ul>
            </div>
        </section>

        <section id="dependency-injection" class="section">
            <h2 class="section-title">依存性注入の活用</h2>
            
            <p>
                依存性注入（DI）は、クラス間の結合度を下げ、テスト容易性を高めるために重要です。
                LaravelのDIコンテナを活用して、コードの柔軟性と保守性を向上させましょう。
            </p>

            <h3>コンストラクタインジェクション</h3>
            <p>
                依存するクラスはコンストラクタで注入し、プロパティに保持します。PHP 8.0以降では
                コンストラクタプロパティプロモーションを使用すると簡潔に記述できます。
            </p>

            <pre><code><?php

namespace App\Http\Controllers;

use App\Services\UserService;
use App\Services\NotificationService;

class UserController extends Controller
{
    public function __construct(
        private UserService $userService,
        private NotificationService $notificationService
    ) {
    }
    
    // コントローラーメソッド...
}</code></pre>

            <h3>インターフェースと実装の分離</h3>
            <p>
                複数の実装が考えられる場合や、モックが必要な場合は、インターフェースを定義して
                サービスプロバイダーで実装をバインドします。
            </p>

            <pre><code><?php

namespace App\Providers;

use App\Contracts\PaymentGatewayInterface;
use App\Services\Payments\StripePaymentGateway;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(PaymentGatewayInterface::class, function ($app) {
            return new StripePaymentGateway(
                config('services.stripe.key'),
                config('services.stripe.secret')
            );
        });
    }
}</code></pre>

            <div class="warning">
                <p><strong>注意点：</strong></p>
                <p>すべてのクラスにインターフェースを作成するのは過剰設計になる可能性があります。変更の可能性が高い部分や、テストでモックが必要な部分に限定して使用しましょう。</p>
            </div>
        </section>

        <section id="error-handling" class="section">
            <h2 class="section-title">エラーハンドリング</h2>
            
            <p>
                効果的なエラーハンドリングは、ユーザー体験と開発者の生産性を向上させる重要な要素です。
                適切な例外処理と明確なエラーメッセージを実装しましょう。
            </p>

            <h3>例外の使い分け</h3>
            <p>
                ドメイン固有の例外クラスを定義し、状況に応じて適切な例外を投げることで、
                エラーの種類を明確に区別できます。
            </p>

            <pre><code><?php

namespace App\Exceptions;

class ShiftOverlapException extends \Exception
{
    protected $message = 'シフトが既存のシフトと重複しています';
}

// サービス内での使用
if ($existingShift) {
    throw new ShiftOverlapException();
}</code></pre>

            <h3>コントローラーでの例外処理</h3>
            <p>
                コントローラーでは、サービス層からの例外を適切にキャッチし、HTTPレスポンスに変換します。
            </p>

            <pre><code>public function store(StoreStaffShiftRequest $request): JsonResponse
{
    try {
        $validated = $request->validated();
        $shift = $this->staffShiftService->createShift($validated);
        
        return response()->json($shift, 201);
    } catch (ShiftOverlapException $e) {
        return response()->json(['error' => $e->getMessage()], 422);
    } catch (\Exception $e) {
        // ログ記録など
        return response()->json(['error' => 'シフト作成に失敗しました'], 500);
    }
}</code></pre>

            <h3>グローバル例外ハンドラー</h3>
            <p>
                繰り返し発生する可能性のある例外は、<code>App\Exceptions\Handler</code>クラスで
                グローバルに処理することも効果的です。
            </p>

            <pre><code><?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    // ...
    
    public function render($request, Throwable $e)
    {
        if ($e instanceof ShiftOverlapException) {
            return response()->json([
                'error' => $e->getMessage()
            ], 422);
        }
        
        return parent::render($request, $e);
    }
}</code></pre>

            <div class="highlight">
                <p><strong>エラーハンドリングのベストプラクティス：</strong></p>
                <ul>
                    <li>ドメイン固有の例外クラスを定義する</li>
                    <li>サービス層では適切な例外を投げ、処理を中断する</li>
                    <li>コントローラーでは例外をHTTPレスポンスに変換する</li>
                    <li>繰り返し発生する例外はグローバルハンドラーで処理する</li>
                    <li>開発者向けの詳細なログと、ユーザー向けの適切なメッセージを分ける</li>
                </ul>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>