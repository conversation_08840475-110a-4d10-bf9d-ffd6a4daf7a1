<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>アーキテクチャガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .architecture-diagram {
            max-width: 100%;
            margin: 2rem 0;
            text-align: center;
        }

        .architecture-diagram img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">アーキテクチャガイド</span>
        </div>

        <h1 class="title">アーキテクチャガイド</h1>
        <p class="subtitle">JTTアプリのレイヤードアーキテクチャの詳細と実装方法</p>

        <section id="overview" class="section">
            <h2 class="section-title">アーキテクチャ概要</h2>
            <p>
                JTTアプリでは<strong>レイヤードアーキテクチャ</strong>を採用し、各コンポーネントの責務を明確に分離しています。
                これにより、テスト容易性が向上し、将来的な機能拡張や変更が容易になります。
            </p>

            <h3>レイヤードアーキテクチャとは？</h3>
            <p>
                レイヤードアーキテクチャは、アプリケーションを複数の「層」（レイヤ）に分けて設計する手法です。
                各レイヤには特定の責務があり、上位レイヤは下位レイヤに依存しますが、下位レイヤは上位レイヤを知りません。
                この依存関係の一方向性により、変更の影響範囲を限定でき、保守性が向上します。
            </p>

            <div class="architecture-diagram">
                <pre style="text-align: center;">
+-------------------+
|   プレゼンテーション層  |  ← ルート、コントローラー、ビュー
+-------------------+
          ↓
+-------------------+
|   ビジネスロジック層  |  ← サービス層、バリデーション
+-------------------+
          ↓
+-------------------+
|   データアクセス層   |  ← モデル、リポジトリ
+-------------------+
          ↓
+-------------------+
|   インフラストラクチャ層 |  ← データベース、外部API、キャッシュ
+-------------------+
                </pre>
            </div>

            <h3>レイヤ間の依存関係</h3>
            <pre><code>Controller → Service → Model → DB</code></pre>
            <p>基本的な依存関係は上記の一方向のみとし、下位レイヤが上位レイヤに直接依存することは避けます。</p>

            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">レイヤード化する利点</div>
                    <ul>
                        <li>責務の明確な分離による可読性向上</li>
                        <li>テスト容易性の向上（サービス層のモック化）</li>
                        <li>ビジネスロジックの再利用</li>
                        <li>データアクセス層の変更が他に影響しない</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <div class="comparison-title">レイヤード化しない場合</div>
                    <ul>
                        <li>コントローラーが肥大化しやすい</li>
                        <li>ビジネスロジックが散在し保守困難に</li>
                        <li>テストが難しく、カバレッジが低下</li>
                        <li>同じロジックを複数の場所で実装する可能性</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="folder-structure" class="section">
            <h2 class="section-title">フォルダ構造</h2>
            <p>
                JTTアプリの標準的なフォルダ構造は以下のとおりです。
                この構造に従うことで、チーム内でのコード検索や理解が容易になります。
            </p>

            <pre><code>app/
├── Http/
│   ├── Controllers/         # コントローラー
│   ├── Middleware/          # ミドルウェア
│   └── Requests/            # フォームリクエスト
├── Services/                # サービス層 (ビジネスロジック)
├── Models/                  # Eloquentモデル
├── Repositories/            # リポジトリ（オプション）
├── Providers/               # サービスプロバイダー
├── Events/                  # イベント
├── Listeners/               # イベントリスナー
└── Exceptions/              # 例外ハンドラー

database/
├── migrations/              # マイグレーションファイル
└── seeders/                 # シーダー

config/                      # 設定ファイル
routes/                      # ルート定義
resources/                   # ビュー、JavaScript、CSS
tests/                       # テスト</code></pre>

            <div class="highlight">
                <p><strong>Tips:</strong> ディレクトリは機能別ではなく、技術的関心事ごとに分類しています。これにより、同種のコンポーネントが一箇所にまとまり、コードナビゲーションが容易になります。</p>
            </div>
        </section>

        <section id="layers" class="section">
            <h2 class="section-title">各レイヤの責務</h2>

            <h3>プレゼンテーション層</h3>
            <p>
                このレイヤはユーザーとのインタラクションを担当します。
                HTTPリクエストの受け取り、バリデーション、レスポンスの構築を行います。
            </p>
            <ul>
                <li><strong>コントローラー</strong>：リクエストの処理とレスポンスの返却</li>
                <li><strong>ミドルウェア</strong>：リクエスト/レスポンスの前処理・後処理</li>
                <li><strong>フォームリクエスト</strong>：入力値のバリデーション</li>
                <li><strong>ビュー</strong>：（Webアプリの場合）HTML生成</li>
            </ul>

            <h3>ビジネスロジック層（サービス層）</h3>
            <p>
                アプリケーションの中核となるビジネスロジックを担当します。
                複数のモデルを跨ぐ処理やドメインルールの実装を行います。
            </p>
            <ul>
                <li><strong>サービスクラス</strong>：ビジネスロジックの実装</li>
                <li><strong>DTOクラス</strong>：レイヤ間のデータ転送</li>
                <li><strong>バリデーションルール</strong>：ビジネスルールの検証</li>
            </ul>

            <h3>データアクセス層</h3>
            <p>
                データの永続化や取得を担当します。
                データベースへのアクセスや外部APIとの通信を抽象化します。
            </p>
            <ul>
                <li><strong>Eloquentモデル</strong>：データベースレコードの表現</li>
                <li><strong>リポジトリ</strong>：（オプション）データアクセスの抽象化</li>
                <li><strong>クエリビルダー</strong>：複雑なクエリの構築</li>
            </ul>

            <h3>インフラストラクチャ層</h3>
            <p>
                アプリケーションの実行環境に関する技術的な詳細を提供します。
                データベース、キャッシュ、ファイルストレージなどの実装を担当します。
            </p>
            <ul>
                <li><strong>データベース</strong>：データの永続化</li>
                <li><strong>キャッシュ</strong>：パフォーマンス向上</li>
                <li><strong>外部API</strong>：他システムとの連携</li>
                <li><strong>ファイルストレージ</strong>：ファイルの保存</li>
            </ul>
        </section>

        <section id="dependency-injection" class="section">
            <h2 class="section-title">依存性注入（DI）</h2>
            <p>
                JTTアプリでは<strong>依存性注入</strong>パターンを積極的に活用しています。
                これにより、コンポーネント間の結合度を下げ、テストが容易になります。
            </p>

            <h3>コンストラクタインジェクション</h3>
            <p>
                依存オブジェクトはコンストラクタを通じて注入します。これにより、依存関係が明示的になり、必要な依存が揃っていることを保証できます。
            </p>

            <pre><code class="language-php">&lt;?php

namespace App\Http\Controllers;

use App\Services\UserService;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {
        // Laravelのサービスコンテナが自動的にUserServiceを注入
    }

    public function show(Request $request, int $id)
    {
        $user = $this->userService->findById($id);
        return view('users.show', ['user' => $user]);
    }
}</code></pre>

            <div class="highlight">
                <p><strong>DIの利点:</strong></p>
                <ul>
                    <li>モックやスタブを使用したテストが容易になる</li>
                    <li>コンポーネントの再利用性が向上する</li>
                    <li>依存関係が明示的になり、コードの理解しやすさが向上する</li>
                    <li>実装の詳細ではなくインターフェースに依存できる</li>
                </ul>
            </div>
        </section>

        <section id="interfaces" class="section">
            <h2 class="section-title">インターフェース</h2>
            <p>
                特に変更の可能性が高い部分や、複数の実装が考えられる場合は、
                <strong>インターフェース</strong>を定義して実装とクライアントコードを分離することを推奨します。
            </p>

            <h3>インターフェースの例</h3>
            <pre><code class="language-php">&lt;?php

namespace App\Contracts;

interface PaymentGatewayInterface
{
    public function charge(float $amount, array $paymentDetails): bool;

    public function refund(string $transactionId, ?float $amount = null): bool;
}</code></pre>

            <h3>実装クラス</h3>
            <pre><code class="language-php">&lt;?php

namespace App\Services\Payments;

use App\Contracts\PaymentGatewayInterface;

class StripePaymentGateway implements PaymentGatewayInterface
{
    public function charge(float $amount, array $paymentDetails): bool
    {
        // Stripe APIを使った実装
    }

    public function refund(string $transactionId, ?float $amount = null): bool
    {
        // Stripe APIを使った実装
    }
}</code></pre>

            <h3>サービスプロバイダでの登録</h3>
            <pre><code class="language-php">&lt;?php

namespace App\Providers;

use App\Contracts\PaymentGatewayInterface;
use App\Services\Payments\StripePaymentGateway;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(PaymentGatewayInterface::class, function ($app) {
            return new StripePaymentGateway(
                config('services.stripe.key'),
                config('services.stripe.secret')
            );
        });
    }
}</code></pre>

            <div class="warning">
                <p><strong>注意:</strong> すべてのクラスにインターフェースを作成するのは過剰設計になる可能性があります。将来的に異なる実装が必要になる可能性が高い場合や、テストでモックが頻繁に必要な場合に限って使用しましょう。</p>
            </div>
        </section>

        <section id="best-practices" class="section">
            <h2 class="section-title">アーキテクチャのベストプラクティス</h2>

            <h3>単一責任の原則</h3>
            <p>
                各クラスは単一の責任を持つべきです。1つのクラスが異なる複数の関心事を持つべきではありません。
                例えば、ユーザー認証とファイルアップロードのロジックを同じクラスに混在させないようにします。
            </p>

            <h3>シンプルさの追求</h3>
            <p>
                過剰な抽象化や複雑なパターンの導入は避け、シンプルさを重視します。
                理解しやすく保守しやすいコードは、洗練された「エレガント」なコードよりも価値があります。
            </p>

            <h3>ファットモデル、スキニーコントローラー</h3>
            <p>
                「ファットモデル、スキニーコントローラー」の原則に従い、可能な限りモデルにロジックを集約し、
                コントローラーはシンプルに保ちます。ただし、モデルが肥大化しすぎる場合は、サービスクラスに
                ロジックを移動します。
            </p>

            <h3>SOLID原則の適用</h3>
            <p>
                オブジェクト指向設計の基本原則であるSOLID原則を適用します：
            </p>
            <ul>
                <li><strong>S</strong>ingle Responsibility（単一責任）</li>
                <li><strong>O</strong>pen/Closed（開放/閉鎖）</li>
                <li><strong>L</strong>iskov Substitution（リスコフの置換）</li>
                <li><strong>I</strong>nterface Segregation（インターフェース分離）</li>
                <li><strong>D</strong>ependency Inversion（依存性逆転）</li>
            </ul>

            <h3>名前付け規則の一貫性</h3>
            <p>
                クラス、メソッド、変数の命名は一貫性を持たせ、その目的や機能が名前から明確に理解できるようにします。
                シンプルで具体的な名前を使い、略語や一般的でない頭字語は避けます。
            </p>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
