<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase連携ガイド | Laravel × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --supabase: #3ecf8e;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--gray-600);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: var(--primary);
        }

        .breadcrumb .separator {
            margin: 0 0.5rem;
            color: var(--gray-400);
        }

        .breadcrumb .current {
            color: var(--gray-800);
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .warning {
            background-color: #fff7ed;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--warning);
        }

        .success {
            background-color: #ecfdf5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--success);
        }

        .danger {
            background-color: #fef2f2;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--danger);
        }

        .supabase-highlight {
            background-color: #f0fdf9;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--supabase);
        }

        code, pre {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            background-color: var(--gray-100);
            border-radius: 0.25rem;
        }

        code {
            padding: 0.2rem 0.4rem;
        }

        pre {
            padding: 1rem;
            overflow-x: auto;
            border: 1px solid var(--gray-200);
            margin: 1rem 0;
        }

        pre code {
            padding: 0;
            background-color: transparent;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .comparison-item {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .comparison-title.good {
            color: var(--success);
        }

        .comparison-title.bad {
            color: var(--danger);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="controller_service_model_manual.html">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="breadcrumb">
            <a href="controller_service_model_manual.html">ホーム</a>
            <span class="separator">/</span>
            <span class="current">Supabase連携ガイド</span>
        </div>

        <h1 class="title">Supabase連携ガイド</h1>
        <p class="subtitle">LaravelとSupabaseの効率的な連携方法と認証・ストレージの活用</p>

        <section id="overview" class="section">
            <h2 class="section-title">Supabaseとは</h2>
            <p>
                Supabaseは、Firebase代替となるオープンソースのバックエンドプラットフォームです。
                PostgreSQLデータベース、認証、ストレージ、リアルタイムサブスクリプション、関数などの
                機能を提供し、モダンなアプリケーション開発を加速します。
            </p>

            <h3>JTTアプリでのSupabase活用</h3>
            <p>
                JTTアプリでは、以下のSupabase機能を主に活用しています：
            </p>
            <ul>
                <li><strong>PostgreSQLデータベース</strong>：高度なデータベース機能とRLSセキュリティ</li>
                <li><strong>認証機能</strong>：ユーザー認証とアクセス管理</li>
                <li><strong>ストレージ</strong>：ファイルアップロードと管理</li>
                <li><strong>Edge Functions</strong>：サーバーレス関数の実行</li>
                <li><strong>リアルタイム</strong>：データ変更のリアルタイム通知</li>
            </ul>

            <div class="supabase-highlight">
                <p><strong>Supabaseの利点：</strong></p>
                <ul>
                    <li>PostgreSQLの強力な機能をフルに活用できる</li>
                    <li>セルフホスティングオプションがある（Pro Tier）</li>
                    <li>リアルタイム機能がすぐに使える</li>
                    <li>RLS（Row Level Security）によるデータ保護</li>
                    <li>オープンソースで透明性が高い</li>
                </ul>
            </div>
        </section>

        <section id="setup" class="section">
            <h2 class="section-title">Laravel-Supabase連携のセットアップ</h2>
            
            <p>
                LaravelとSupabaseを連携するためには、適切なセットアップと設定が必要です。
                このセクションでは、基本的なセットアップ手順を解説します。
            </p>

            <h3>Supabaseプロジェクトの作成</h3>
            <ol>
                <li><a href="https://app.supabase.io" target="_blank">Supabaseダッシュボード</a>にアクセス</li>
                <li>「New Project」をクリックして新規プロジェクトを作成</li>
                <li>プロジェクト名、パスワード、リージョンを設定</li>
                <li>プロジェクト作成後、「Project Settings」からAPI URLとキーを取得</li>
            </ol>

            <h3>環境変数の設定</h3>
            <p>
                Supabaseの接続情報を<code>.env</code>ファイルに設定します。
            </p>
            <pre><code># .env ファイル
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SECRET=your-supabase-service-role-key</code></pre>

            <h3>Supabase PHPクライアントのインストール</h3>
            <p>
                公式のSupabase PHP SDKをインストールします。
            </p>
            <pre><code>composer require supabase/supabase-php</code></pre>

            <h3>Supabaseサービスプロバイダの作成</h3>
            <p>
                Laravelで簡単にSupabaseを使用できるように、サービスプロバイダを作成します。
            </p>
            <pre><code><?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Supabase\CreateClient;

class SupabaseServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('supabase', function ($app) {
            return CreateClient(
                env('SUPABASE_URL'),
                env('SUPABASE_KEY')
            );
        });
    }

    public function provides()
    {
        return ['supabase'];
    }
}</code></pre>

            <h3>サービスプロバイダの登録</h3>
            <p>
                作成したサービスプロバイダを<code>config/app.php</code>に登録します。
            </p>
            <pre><code>'providers' => [
    // 他のプロバイダ...
    App\Providers\SupabaseServiceProvider::class,
],</code></pre>

            <h3>ファサードの作成（オプション）</h3>
            <p>
                より簡単にSupabaseクライアントを使用するために、ファサードを作成することもできます。
            </p>
            <pre><code><?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

class Supabase extends Facade
{
    protected static function getFacadeAccessor()
    {
        return 'supabase';
    }
}</code></pre>

            <p>
                ファサードを<code>config/app.php</code>のaliasesセクションに登録します。
            </p>
            <pre><code>'aliases' => [
    // 他のエイリアス...
    'Supabase' => App\Facades\Supabase::class,
],</code></pre>

            <div class="highlight">
                <p><strong>Tips：</strong></p>
                <p>環境ごとに異なるSupabaseプロジェクトを使い分けることができます。開発環境用、テスト環境用、本番環境用にそれぞれ異なるプロジェクトを作成し、環境に応じて接続情報を切り替えることをおすすめします。</p>
            </div>
        </section>

        <section id="database" class="section">
            <h2 class="section-title">データベースとマイグレーション</h2>
            
            <p>
                SupabaseはPostgreSQLデータベースを提供しており、Laravelのマイグレーションと連携させることができます。
                JTTアプリでは、マイグレーションファイルを両方の環境で使用できるように設計しています。
            </p>

            <h3>マイグレーションの命名規則</h3>
            <p>
                JTTアプリでのマイグレーションファイルの命名規則は以下の通りです：
            </p>
            <pre><code>YYYYMMDDHHMM_create_table_name.php  # Laravelマイグレーション
YYYYMMDDHHMM_create_table_name.sql  # Supabaseマイグレーション</code></pre>

            <p>
                Laravel用とSupabase用のマイグレーションファイルを対応させることで、両環境での整合性を維持します。
            </p>

            <h3>PostgreSQL固有の機能対応</h3>
            <p>
                PostgreSQLには独自の機能があり、Laravelのマイグレーションでは直接対応していない場合があります。
                そのような場合は、<code>DB::statement</code>を使用してPostgreSQL固有のSQLを実行します。
            </p>

            <pre><code>// Laravelマイグレーション
public function up()
{
    Schema::create('staff_shifts', function (Blueprint $table) {
        $table->id();
        $table->foreignId('staff_id')->constrained('users');
        $table->timestamp('start_time');
        $table->timestamp('end_time');
        $table->string('role_col'); // 予約語回避のため _col サフィックス
        $table->timestamps();
    });
    
    // PostgreSQL固有の機能
    DB::statement('COMMENT ON TABLE staff_shifts IS \'スタッフのシフト情報\'');
    DB::statement('COMMENT ON COLUMN staff_shifts.role_col IS \'担当役割\'');
}</code></pre>

            <pre><code>-- Supabaseマイグレーション
CREATE TABLE IF NOT EXISTS "core"."staff_shifts" (
    "id" BIGSERIAL PRIMARY KEY,
    "staff_id" BIGINT NOT NULL REFERENCES auth.users(id),
    "start_time" TIMESTAMP WITH TIME ZONE NOT NULL,
    "end_time" TIMESTAMP WITH TIME ZONE NOT NULL,
    "role_col" TEXT NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now(),
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now()
);

COMMENT ON TABLE "core"."staff_shifts" IS 'スタッフのシフト情報';
COMMENT ON COLUMN "core"."staff_shifts"."role_col" IS '担当役割';</code></pre>

            <h3>ローカル開発でのSupabase</h3>
            <p>
                Supabase CLIを使用して、ローカル環境でSupabaseを実行することができます。
                これにより、開発時にリモートのSupabaseプロジェクトに依存せずに開発できます。
            </p>
            <pre><code># Supabase CLIのインストール
npm install -g supabase

# ローカルSupabaseの起動
supabase start

# マイグレーションの適用
supabase db push</code></pre>

            <div class="warning">
                <p><strong>注意点：</strong></p>
                <p>PostgreSQLの予約語（user, order, tableなど）をカラム名として使用すると問題が発生します。JTTアプリでは、予約語を使用する場合は<code>_col</code>サフィックスをつけるルールを採用しています（例：<code>user_col</code>、<code>order_col</code>）。</p>
            </div>
        </section>

        <section id="auth" class="section">
            <h2 class="section-title">認証システム統合</h2>
            
            <p>
                Supabaseは強力な認証機能を提供しており、LaravelのAuth機能と統合することができます。
                JTTアプリでは、Supabase認証とLaravelの認証システムを連携させています。
            </p>

            <h3>Supabase認証の基本設定</h3>
            <p>
                Supabaseダッシュボードの「Authentication」セクションから、以下の設定を行います：
            </p>
            <ol>
                <li>有効にする認証プロバイダを選択（Email/Password, Google, GitHubなど）</li>
                <li>サイトURLとリダイレクトURLを設定</li>
                <li>必要に応じてメールテンプレートをカスタマイズ</li>
            </ol>

            <h3>LaravelでのSupabase認証の実装</h3>
            <p>
                Supabase認証をLaravelに統合する基本的なフローを実装します。
            </p>

            <pre><code><?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Facades\Supabase;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        try {
            // Supabaseで認証
            $response = Supabase::auth()->signIn([
                'email' => $credentials['email'],
                'password' => $credentials['password'],
            ]);

            // Supabaseからユーザー情報を取得
            $supabaseUser = $response->user;

            // Laravelユーザーを取得または作成
            $user = User::firstOrCreate(
                ['email' => $supabaseUser->email],
                [
                    'name' => $supabaseUser->user_metadata->name ?? 'User',
                    'supabase_id' => $supabaseUser->id,
                ]
            );

            // Laravelで認証
            Auth::login($user);

            return redirect()->intended('dashboard');

        } catch (\Exception $e) {
            return back()->withErrors([
                'email' => 'ログイン情報が正しくありません。',
            ]);
        }
    }
}</code></pre>

            <h3>Supabase JWT認証の活用</h3>
            <p>
                SupabaseのJWTトークンを利用して、APIリクエストの認証を行うことができます。
            </p>

            <pre><code><?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Facades\Supabase;
use App\Models\User;

class SupabaseAuth
{
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json(['error' => '認証が必要です'], 401);
        }

        try {
            // JWTトークンを検証
            $supabaseUser = Supabase::auth()->user($token);
            
            // ユーザーをデータベースから取得
            $user = User::where('supabase_id', $supabaseUser->id)->first();
            
            if (!$user) {
                return response()->json(['error' => 'ユーザーが見つかりません'], 404);
            }
            
            // リクエストにユーザーを設定
            $request->merge(['user' => $user]);
            
            return $next($request);
        } catch (\Exception $e) {
            return response()->json(['error' => '無効なトークンです'], 401);
        }
    }
}</code></pre>

            <div class="highlight">
                <p><strong>認証統合のベストプラクティス：</strong></p>
                <ul>
                    <li>Supabase認証とLaravel認証を同期させる仕組みを実装する</li>
                    <li>Supabase JWTトークンの有効期限を適切に管理する</li>
                    <li>パスワードリセットなどの操作はSupabaseの機能を活用する</li>
                    <li>OAuth認証（Google, GitHub）はSupabase側で設定し、Laravel側で連携する</li>
                </ul>
            </div>
        </section>

        <section id="storage" class="section">
            <h2 class="section-title">ストレージ機能の活用</h2>
            
            <p>
                Supabaseのストレージ機能を使用すると、ファイルのアップロードや管理が容易になります。
                JTTアプリでは、ユーザーアバターや添付ファイルなどの保存にSupabaseストレージを活用しています。
            </p>

            <h3>ストレージバケットの設定</h3>
            <p>
                Supabaseダッシュボードの「Storage」セクションから、バケットを作成し、アクセス権限を設定します。
                JTTアプリでは以下のバケットを使用しています：
            </p>
            <ul>
                <li><strong>public</strong>：公開アクセス可能なファイル（アバターなど）</li>
                <li><strong>private</strong>：認証されたユーザーのみアクセス可能なファイル（個人文書など）</li>
                <li><strong>secure</strong>：特定の権限を持つユーザーのみアクセス可能な重要文書</li>
            </ul>

            <h3>Storage Policiesの設定</h3>
            <p>
                Row Level Security（RLS）ポリシーを使用して、バケットやファイルへのアクセス制御を行います。
            </p>
            <pre><code>-- public バケットのRLSポリシー
CREATE POLICY "Public Access"
ON storage.objects FOR SELECT
USING (bucket_id = 'public');

-- private バケットのRLSポリシー
CREATE POLICY "Auth Users Only"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'private' 
  AND auth.uid() = owner
);

-- ファイルアップロードポリシー
CREATE POLICY "Allow Uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id IN ('public', 'private') 
  AND auth.uid() IS NOT NULL
);</code></pre>

            <h3>LaravelからのSupabaseストレージ操作</h3>
            <pre><code><?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Facades\Supabase;

class FileController extends Controller
{
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB最大
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $fileContent = file_get_contents($file->getRealPath());
        
        try {
            // Supabaseにファイルをアップロード
            $result = Supabase::storage()
                ->from('private')
                ->upload($fileName, $fileContent, [
                    'content-type' => $file->getMimeType(),
                ]);
            
            // アップロード成功時の処理
            return response()->json([
                'success' => true,
                'path' => $result->path,
                'url' => Supabase::storage()->from('private')->getPublicUrl($result->path),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'ファイルアップロードに失敗しました：' . $e->getMessage(),
            ], 500);
        }
    }
    
    public function download($path)
    {
        try {
            // ファイルのダウンロードURL（署名付き）を取得
            $url = Supabase::storage()
                ->from('private')
                ->createSignedUrl($path, 60); // 60秒間有効
            
            return redirect($url);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'ファイルにアクセスできません：' . $e->getMessage(),
            ], 404);
        }
    }
}</code></pre>

            <h3>ファイルの公開アクセスと署名付きURL</h3>
            <p>
                公開バケットのファイルには直接アクセスでき、非公開バケットのファイルには署名付きURLを
                使用してアクセスします。
            </p>
            <pre><code>// 公開ファイルのURL
$publicUrl = Supabase::storage()->from('public')->getPublicUrl('avatar.png');

// 署名付きURL（一時的なアクセス権を付与）
$signedUrl = Supabase::storage()->from('private')->createSignedUrl(
    'document.pdf',
    60*60 // 有効期間（秒）
);</code></pre>

            <div class="warning">
                <p><strong>注意点：</strong></p>
                <p>ファイル名には適切なプレフィックスや時間情報を付けるなど、一意性を確保してください。また、アップロードするファイルのサイズや種類を適切にバリデーションすることが重要です。</p>
            </div>
        </section>

        <section id="realtime" class="section">
            <h2 class="section-title">リアルタイムサブスクリプション</h2>
            
            <p>
                Supabaseは、PostgreSQLのPub/Sub機能を使用してリアルタイム更新をクライアントに提供します。
                これにより、データの変更をリアルタイムでフロントエンドに反映することができます。
            </p>

            <h3>リアルタイム機能の有効化</h3>
            <p>
                テーブルごとにリアルタイム機能を有効にする必要があります。
            </p>
            <pre><code>-- テーブルのリアルタイム機能を有効化
ALTER TABLE core.staff_shifts
REPLICA IDENTITY FULL;  -- 変更前後のデータを含める

-- パブリケーションの作成または更新
ALTER PUBLICATION supabase_realtime 
ADD TABLE core.staff_shifts;</code></pre>

            <h3>JavaScriptでのリアルタイムサブスクリプション</h3>
            <p>
                フロントエンド（VueやReactなど）からSupabaseのリアルタイム機能を利用する例：
            </p>
            <pre><code>import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'https://your-project-id.supabase.co',
  'your-anon-key'
)

// リアルタイムサブスクリプションの設定
const shifts = supabase
  .channel('custom-channel')
  .on(
    'postgres_changes',
    {
      event: '*',       // INSERT, UPDATE, DELETE
      schema: 'core',
      table: 'staff_shifts',
      // filter: 'staff_id=eq.123'  // フィルタリングも可能
    },
    (payload) => {
      console.log('Change received!', payload)
      // UIの更新処理
      updateShiftsList(payload)
    }
  )
  .subscribe()</code></pre>

            <h3>Laravel Echoとの連携</h3>
            <p>
                Laravel Echoを使用してSupabaseリアルタイム機能と連携することもできます。
            </p>
            <pre><code>// resources/js/bootstrap.js
import Echo from 'laravel-echo';
import { createClient } from '@supabase/supabase-js'

window.Echo = new Echo({
    broadcaster: 'supabase',
    client: createClient(
        process.env.MIX_SUPABASE_URL,
        process.env.MIX_SUPABASE_KEY
    )
});

// 使用例
Echo.channel('staff-shifts')
    .listen('ShiftUpdated', (e) => {
        console.log(e);
        // UIを更新
    });</code></pre>

            <div class="highlight">
                <p><strong>リアルタイム機能のベストプラクティス：</strong></p>
                <ul>
                    <li>必要なテーブルにのみリアルタイム機能を有効化する</li>
                    <li>大量のデータ更新が予想される場合は、適切なフィルタリングを行う</li>
                    <li>クライアント側で適切なエラーハンドリングを実装する</li>
                    <li>接続が切れた場合の再接続ロジックを実装する</li>
                </ul>
            </div>
        </section>

        <section id="functions" class="section">
            <h2 class="section-title">Edge Functions と RPC</h2>
            
            <p>
                SupabaseのEdge Functions（サーバーレス関数）とRPC（Remote Procedure Call）を使用すると、
                クライアントから直接呼び出せるバックエンドロジックを実装できます。
            </p>

            <h3>PostgreSQL関数（RPC）の作成</h3>
            <p>
                PostgreSQL関数を作成し、Supabaseから直接呼び出せるようにします。
            </p>
            <pre><code>-- マイグレーションファイル: 20250513103000_create_functions.sql
CREATE OR REPLACE FUNCTION public.get_staff_shifts(
  p_staff_id BIGINT,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS SETOF core.staff_shifts AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM core.staff_shifts
  WHERE staff_id = p_staff_id
  AND start_time::DATE >= p_start_date
  AND end_time::DATE <= p_end_date
  ORDER BY start_time;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;</code></pre>

            <p>
                セキュリティ定義者関数（SECURITY DEFINER）として作成することで、
                関数の所有者の権限でクエリが実行されます。これにより、RLSを迂回して
                データにアクセスすることも可能です。
            </p>

            <h3>Edge Functionsの作成</h3>
            <p>
                TypeScriptを使用してEdge Functionsを作成し、より複雑なロジックを実装できます。
            </p>
            <pre><code>// supabase/functions/jotform_to_asana/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

serve(async (req) => {
  try {
    const { formData } = await req.json()
    
    // Supabaseクライアントの作成
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseKey)
    
    // フォームデータの処理
    const result = await processJotFormData(formData, supabase)
    
    return new Response(
      JSON.stringify(result),
      { headers: { 'Content-Type': 'application/json' } },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { 'Content-Type': 'application/json' } },
    )
  }
})

async function processJotFormData(formData, supabase) {
  // JotFormデータの処理とAsanaへの送信ロジック
  // ...
  
  // 処理結果をデータベースに保存
  const { data, error } = await supabase
    .from('jotform_submissions')
    .insert({
      form_id: formData.formID,
      submission_id: formData.submissionID,
      processed: true,
      payload: formData
    })
  
  if (error) throw error
  
  return { success: true, data }
}</code></pre>

            <h3>LaravelからのEdge Functions呼び出し</h3>
            <pre><code><?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use App\Facades\Supabase;

class ProcessJotFormWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $formData;

    public function __construct(array $formData)
    {
        $this->formData = $formData;
    }

    public function handle()
    {
        // Edge Function呼び出し
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('SUPABASE_KEY'),
            'Content-Type' => 'application/json',
        ])->post(
            env('SUPABASE_URL') . '/functions/v1/jotform_to_asana',
            ['formData' => $this->formData]
        );

        if (!$response->successful()) {
            throw new \Exception('Edge Function呼び出しに失敗しました: ' . $response->body());
        }

        return $response->json();
    }
}</code></pre>

            <div class="warning">
                <p><strong>セキュリティに関する注意点：</strong></p>
                <ul>
                    <li>Edge Functionsは公開エンドポイントとなるため、適切な認証と入力バリデーションを実装する</li>
                    <li>機密情報はSupabase環境変数として安全に保管する</li>
                    <li>SECURITY DEFINERを使用する場合は、最小権限の原則に従い、必要最小限の操作のみ許可する</li>
                </ul>
            </div>
        </section>

        <section id="local-development" class="section">
            <h2 class="section-title">ローカル開発環境の構築</h2>
            
            <p>
                効率的な開発のためには、ローカル環境でSupabaseを実行できるようにすることが重要です。
                JTTアプリでは、Supabase CLIを使用してローカル開発環境を構築しています。
            </p>

            <h3>Supabase CLIのインストールと設定</h3>
            <pre><code># npmまたはyarnを使用してインストール
npm install -g supabase

# Supabase CLIの初期化
supabase init</code></pre>

            <h3>ローカルSupabaseの起動と停止</h3>
            <pre><code># ローカルSupabaseインスタンスの起動
supabase start

# 状態確認
supabase status

# 停止
supabase stop</code></pre>

            <h3>マイグレーションの適用</h3>
            <pre><code># ローカルDBにマイグレーションを適用
supabase db push</code></pre>

            <h3>環境設定とCIへの統合</h3>
            <p>
                <code>supabase/config.toml</code>ファイルでSupabaseの設定をカスタマイズできます。
            </p>
            <pre><code>[api]
# API URLとport設定
port = 54321
schemas = ["public", "storage", "core"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
# データベース設定
port = 54322
shadow_port = 54320
major_version = 15

[studio]
# 管理画面の設定
port = 54323

[auth]
# 認証設定
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_signup = true</code></pre>

            <h3>CI/CDパイプラインとの統合</h3>
            <p>
                GitHub ActionsなどのCI/CDパイプラインでSupabase CLIを使用して自動テストや
                デプロイを行うことができます。
            </p>
            <pre><code># .github/workflows/db-push.yml
name: Supabase DB Push

on:
  push:
    branches: [ main ]
    paths:
      - 'supabase/migrations/**'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: 1.154.0
      
      - name: Deploy Migrations
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_ID }} \
            --password ${{ secrets.SUPABASE_DB_PASSWORD }}
          supabase db push</code></pre>

            <div class="highlight">
                <p><strong>ローカル開発のベストプラクティス：</strong></p>
                <ul>
                    <li>ローカル環境のデータは定期的にリセットして、クリーンな状態で開発する</li>
                    <li>マイグレーションファイルはGitで管理し、チーム全体で共有する</li>
                    <li>シードデータを用意して、開発環境を簡単に再現できるようにする</li>
                    <li>本番データベースの変更は、必ずマイグレーションファイルを通じて行う</li>
                </ul>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>