<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> × Supabase 開発マニュアル</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #6b7280;
            --success: #16a34a;
            --danger: #dc2626;
            --warning: #f59e0b;
            --info: #0ea5e9;
            --light: #f3f4f6;
            --dark: #1f2937;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: #f8fafc;
            padding: 0;
            font-size: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .title {
            text-align: center;
            margin: 2rem 0;
            color: var(--gray-900);
        }

        .subtitle {
            color: var(--gray-600);
            font-weight: normal;
            font-size: 1.1rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .section {
            background-color: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        .highlight {
            background-color: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid var(--primary);
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background-color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .card-description {
            color: var(--gray-600);
            margin-bottom: 1rem;
            flex-grow: 1;
        }

        .card-link {
            background-color: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            transition: background-color 0.3s;
            text-align: center;
        }

        .card-link:hover {
            background-color: var(--gray-700);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
        }

        footer {
            background-color: white;
            border-top: 1px solid var(--gray-200);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            color: var(--gray-600);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .footer-links a {
            color: var(--gray-600);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        /* Back to top button */
        #back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
            display: none;
        }

        #back-to-top:hover {
            background-color: var(--gray-700);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <a class="logo" href="#">JTT 開発マニュアル</a>
            <div class="nav-links">
                <a href="architecture_guide.html">アーキテクチャ</a>
                <a href="controller_service_guide.html">コントローラー/サービス</a>
                <a href="models_guide.html">モデル</a>
                <a href="routes_api_guide.html">ルート/API</a>
                <a href="supabase_guide.html">Supabase</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <h1 class="title">Laravel × Supabase 開発マニュアル</h1>
        <p class="subtitle">PHP 8.3.* + Laravel 12.x の効率的な開発ガイド</p>

        <section id="introduction" class="section">
            <h2 class="section-title">はじめに</h2>
            <p>
                このマニュアルは、JTTアプリケーション開発におけるベストプラクティスを初学者にもわかりやすく解説しています。
                Laravel 12.xとSupabaseを組み合わせた効率的な開発手法と、プロジェクト特有の規約について学びましょう。
            </p>
            <div class="highlight">
                <p><strong>このマニュアルの目的:</strong></p>
                <ul>
                    <li>開発者間での一貫した実装手法の確立</li>
                    <li>新規参画者への速やかな知識移転</li>
                    <li>保守性と拡張性の高いコード構造の維持</li>
                </ul>
            </div>
        </section>

        <section id="versions" class="section">
            <h2 class="section-title">バージョン固定</h2>
            <p>
                プロジェクト全体で統一されたバージョンを使用することで、「自分の環境では動くのに」という問題を防ぎます。
                以下のバージョンは<strong>厳格に固定</strong>されており、開発・CI環境でも同一バージョンが使用されます。
            </p>
            <table>
                <thead>
                    <tr>
                        <th>技術</th>
                        <th>バージョン</th>
                        <th>備考</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PHP</td>
                        <td><code>8.3.*</code></td>
                        <td>8.4は非対応（厳格に8.3系に固定）</td>
                    </tr>
                    <tr>
                        <td>Laravel</td>
                        <td><code>12.x</code></td>
                        <td>セキュリティサポートは2027年2月24日まで</td>
                    </tr>
                    <tr>
                        <td>Supabase CLI</td>
                        <td><code>1.154.0</code></td>
                        <td>ローカル開発環境で必須</td>
                    </tr>
                </tbody>
            </table>
            <div class="highlight">
                <p><strong>なぜバージョンを固定するのか:</strong></p>
                <p>バージョン固定により、開発環境の差異によるバグを未然に防ぎ、CI/CDパイプラインの一貫性も確保できます。<code>.tool-versions</code>と<code>composer.json</code>の両方でPHPバージョンを固定しています。</p>
            </div>
        </section>

        <section id="guides" class="section">
            <h2 class="section-title">開発ガイド</h2>
            <p>
                JTTアプリの開発に必要な各種ガイドを用意しています。
                各カードをクリックして、詳細な説明にアクセスしてください。
            </p>

            <div class="card-grid">
                <div class="card">
                    <div class="card-icon">📐</div>
                    <h3 class="card-title">アーキテクチャガイド</h3>
                    <p class="card-description">
                        レイヤードアーキテクチャの詳細と、各レイヤの責務について解説します。
                        フォルダ構造やコンポーネント間の依存関係について学びましょう。
                    </p>
                    <a href="architecture_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🎮</div>
                    <h3 class="card-title">コントローラー＆サービス</h3>
                    <p class="card-description">
                        コントローラーとサービス層の正しい実装方法と責務分離について解説します。
                        良い例と悪い例を比較して効率的な設計を学びましょう。
                    </p>
                    <a href="controller_service_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🗄️</div>
                    <h3 class="card-title">モデルガイド</h3>
                    <p class="card-description">
                        Eloquentモデルの効率的な実装方法とリレーションシップの設計について解説します。
                        アクセサやミューテタの活用法を学びましょう。
                    </p>
                    <a href="models_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🛣️</div>
                    <h3 class="card-title">ルート＆API設計</h3>
                    <p class="card-description">
                        WebとAPIのルート設計の原則と実装手法について解説します。
                        RESTful APIの設計原則と命名規則について学びましょう。
                    </p>
                    <a href="routes_api_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🔌</div>
                    <h3 class="card-title">Supabase連携</h3>
                    <p class="card-description">
                        LaravelとSupabaseの効率的な連携方法と認証・ストレージの活用について解説します。
                        ローカル開発環境の構築方法も学びましょう。
                    </p>
                    <a href="supabase_guide.html" class="card-link">詳細を見る</a>
                </div>

                <div class="card">
                    <div class="card-icon">🧪</div>
                    <h3 class="card-title">テスト戦略</h3>
                    <p class="card-description">
                        単体テスト、統合テスト、E2Eテストの実装方法と効率的なテスト戦略について解説します。
                        自動テストの重要性を学びましょう。
                    </p>
                    <a href="testing_guide.html" class="card-link">詳細を見る</a>
                </div>
            </div>
        </section>

        <section id="workflow" class="section">
            <h2 class="section-title">開発ワークフロー</h2>
            <p>
                JTTアプリの開発作業は以下のフローに従って進めます。
                ローカル環境からCI/CDを通じた本番デプロイまでの流れを確認しましょう。
            </p>
            <ol>
                <li><strong>環境構築</strong>：<code>PHP 8.3.*</code>とSupabase CLIをインストール</li>
                <li><strong>ローカルデータベース起動</strong>：<code>supabase start</code></li>
                <li><strong>マイグレーション実行</strong>：<code>php artisan migrate</code></li>
                <li><strong>スキーマ更新確認</strong>：<code>supabase db push --local --dry-run</code></li>
                <li><strong>機能実装</strong>：コントローラー、サービス、モデルの実装</li>
                <li><strong>ユニットテスト作成</strong>：<code>php artisan test</code></li>
                <li><strong>静的解析</strong>：<code>./vendor/bin/phpstan analyse</code></li>
                <li><strong>E2Eテスト</strong>：<code>npm run test:e2e</code></li>
                <li><strong>CI実行</strong>：GitHub Actions経由でテスト実行</li>
                <li><strong>本番デプロイ</strong>：CI GREENからの自動デプロイ</li>
            </ol>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>JTT アプリケーション開発マニュアル &copy; 2025</p>
            <div class="footer-links">
                <a href="../CodingStandards.md">コーディング規約</a>
                <a href="../CI_CD_Testing.md">CI/CD & テスト</a>
                <a href="../Supabase_Guidelines.md">Supabase ガイドライン</a>
            </div>
        </div>
    </footer>

    <div id="back-to-top">↑</div>

    <script>
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Tab functionality for any future tabs
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');

                // Hide all tab contents
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Deactivate all tab buttons
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Activate clicked tab and its content
                button.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    </script>
</body>
</html>
