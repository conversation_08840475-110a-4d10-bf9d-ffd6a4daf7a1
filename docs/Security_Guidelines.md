# セキュリティガイドライン
## Laravel + Supabase アプリケーションのセキュリティ対策

### 概要
このガイドでは、JTT-Appsプロジェクトにおけるセキュリティ対策の実装方法を詳しく説明します。

## 基本セキュリティ原則

### 1. 多層防御（Defense in Depth）
- **入力検証**: すべての入力データを検証
- **出力エスケープ**: すべての出力データをエスケープ
- **認証・認可**: 適切なアクセス制御
- **暗号化**: 機密データの保護

### 2. 最小権限の原則
- ユーザーには必要最小限の権限のみ付与
- データベースアクセスは必要な範囲に限定
- APIエンドポイントは適切に保護

## 主要セキュリティ対策

### 1. XSS（Cross-Site Scripting）対策

#### Bladeテンプレートでの適切なエスケープ
```blade
{{-- ✅ 良い例：自動エスケープ --}}
<h1>{{ $userInput }}</h1>
<p>{{ $user->name }}</p>

{{-- ❌ 悪い例：エスケープなし --}}
<h1>{!! $userInput !!}</h1>

{{-- ✅ 信頼できるHTMLの場合 --}}
<div>{!! Str::markdown($trustedContent) !!}</div>

{{-- ✅ HTMLタグを含む場合の適切な処理 --}}
<div>{!! strip_tags($content, '<p><br><strong><em>') !!}</div>
```

#### JavaScript内での変数使用
```blade
{{-- ✅ 良い例：JSONエンコード --}}
<script>
    const userData = @json($user);
    const message = @json($message);
</script>

{{-- ❌ 悪い例：直接埋め込み --}}
<script>
    const userName = "{{ $user->name }}"; // XSS脆弱性
</script>
```

### 2. CSRF（Cross-Site Request Forgery）対策

#### フォームでのCSRF保護
```blade
{{-- ✅ 必須：CSRFトークンの設定 --}}
<form method="POST" action="{{ route('staff-shifts.store') }}">
    @csrf
    <!-- フォーム内容 -->
</form>

{{-- ✅ メタタグでの設定（Ajax用） --}}
<meta name="csrf-token" content="{{ csrf_token() }}">
```

#### AjaxリクエストでのCSRF対策
```javascript
// ✅ jQuery使用時
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// ✅ Axios使用時
axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

// ✅ Fetch API使用時
fetch('/api/staff-shifts', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify(data)
});
```

### 3. SQLインジェクション対策

#### Eloquent ORMの使用
```php
// ✅ 良い例：Eloquent ORM
$shifts = StaffShift::where('staff_id', $staffId)
    ->whereBetween('start_time', [$startDate, $endDate])
    ->get();

// ✅ 良い例：クエリビルダーでのパラメータバインディング
$shifts = DB::table('staff_shifts')
    ->where('staff_id', '=', $staffId)
    ->get();

// ❌ 悪い例：生のSQL文字列結合
$shifts = DB::select("SELECT * FROM staff_shifts WHERE staff_id = " . $staffId);
```

#### 動的クエリの安全な実装
```php
// ✅ 良い例：パラメータバインディング
public function searchShifts(array $filters): Collection
{
    $query = StaffShift::query();

    if (!empty($filters['staff_id'])) {
        $query->where('staff_id', $filters['staff_id']);
    }

    if (!empty($filters['date_from'])) {
        $query->where('start_time', '>=', $filters['date_from']);
    }

    if (!empty($filters['role'])) {
        $query->where('role_col', $filters['role']);
    }

    return $query->get();
}
```

### 4. 認証・認可

#### Policyを使用した認可制御
```php
// app/Policies/StaffShiftPolicy.php
<?php

namespace App\Policies;

use App\Models\User;
use App\Models\StaffShift;

class StaffShiftPolicy
{
    public function view(User $user, StaffShift $shift): bool
    {
        // 自分のシフトまたは管理者のみ閲覧可能
        return $user->id === $shift->staff_id || $user->isAdmin();
    }

    public function create(User $user): bool
    {
        // 認証済みユーザーのみ作成可能
        return $user->exists;
    }

    public function update(User $user, StaffShift $shift): bool
    {
        // 自分のシフトまたは管理者のみ更新可能
        return $user->id === $shift->staff_id || $user->isAdmin();
    }

    public function delete(User $user, StaffShift $shift): bool
    {
        // 管理者のみ削除可能
        return $user->isAdmin();
    }
}
```

#### Bladeテンプレートでの認可チェック
```blade
{{-- ✅ 権限チェック --}}
@can('create', App\Models\StaffShift::class)
    <a href="{{ route('staff-shifts.create') }}" class="btn btn-primary">
        新規作成
    </a>
@endcan

{{-- ✅ 所有者チェック --}}
@can('update', $shift)
    <a href="{{ route('staff-shifts.edit', $shift) }}" class="btn btn-secondary">
        編集
    </a>
@endcan

{{-- ✅ 管理者チェック --}}
@can('delete', $shift)
    <form method="POST" action="{{ route('staff-shifts.destroy', $shift) }}">
        @csrf
        @method('DELETE')
        <button type="submit" class="btn btn-danger">削除</button>
    </form>
@endcan
```

#### コントローラーでの認可チェック
```php
// app/Http/Controllers/StaffShiftController.php
<?php

namespace App\Http\Controllers;

use App\Models\StaffShift;
use App\Http\Requests\StoreStaffShiftRequest;

class StaffShiftController extends Controller
{
    public function show(StaffShift $shift)
    {
        $this->authorize('view', $shift);
        
        return view('staff-shifts.show', compact('shift'));
    }

    public function store(StoreStaffShiftRequest $request)
    {
        $this->authorize('create', StaffShift::class);
        
        // シフト作成処理
    }

    public function update(StoreStaffShiftRequest $request, StaffShift $shift)
    {
        $this->authorize('update', $shift);
        
        // シフト更新処理
    }
}
```

### 5. 入力検証

#### FormRequestによる厳密なバリデーション
```php
// app/Http/Requests/StoreStaffShiftRequest.php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreStaffShiftRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', StaffShift::class);
    }

    public function rules(): array
    {
        return [
            'staff_id' => [
                'required',
                'integer',
                'exists:users,id',
                Rule::in($this->getAllowedStaffIds()),
            ],
            'start_time' => [
                'required',
                'date',
                'after:now',
                'before:end_time',
            ],
            'end_time' => [
                'required',
                'date',
                'after:start_time',
                'before:' . now()->addMonths(3)->toDateString(),
            ],
            'role_col' => [
                'required',
                'string',
                Rule::in(['cashier', 'kitchen', 'floor']),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'staff_id.required' => 'スタッフを選択してください。',
            'staff_id.exists' => '有効なスタッフを選択してください。',
            'start_time.required' => '開始時間を入力してください。',
            'start_time.after' => '開始時間は現在時刻より後に設定してください。',
            'end_time.required' => '終了時間を入力してください。',
            'end_time.after' => '終了時間は開始時間より後に設定してください。',
            'role_col.required' => '役割を選択してください。',
            'role_col.in' => '有効な役割を選択してください。',
        ];
    }

    private function getAllowedStaffIds(): array
    {
        // 現在のユーザーがアクセス可能なスタッフIDのリストを返す
        return $this->user()->isAdmin() 
            ? User::pluck('id')->toArray()
            : [$this->user()->id];
    }
}
```

### 6. Supabase Row Level Security (RLS)

#### RLSポリシーの設定
```sql
-- スタッフシフトテーブルのRLS設定
ALTER TABLE core.staff_shifts ENABLE ROW LEVEL SECURITY;

-- 自分のシフトのみ閲覧可能
CREATE POLICY "Users can view own shifts" ON core.staff_shifts
    FOR SELECT USING (auth.uid()::text = staff_id::text);

-- 自分のシフトのみ作成可能
CREATE POLICY "Users can create own shifts" ON core.staff_shifts
    FOR INSERT WITH CHECK (auth.uid()::text = staff_id::text);

-- 自分のシフトのみ更新可能
CREATE POLICY "Users can update own shifts" ON core.staff_shifts
    FOR UPDATE USING (auth.uid()::text = staff_id::text);

-- 管理者のみ削除可能
CREATE POLICY "Admins can delete shifts" ON core.staff_shifts
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' = 'admin'
        )
    );
```

### 7. セキュリティヘッダー

#### ミドルウェアでのセキュリティヘッダー設定
```php
// app/Http/Middleware/SecurityHeaders.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';");

        return $response;
    }
}
```

### 8. ログとモニタリング

#### セキュリティイベントのログ記録
```php
// app/Services/SecurityLogger.php
<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class SecurityLogger
{
    public static function logFailedLogin(string $email, string $ip): void
    {
        Log::warning('Failed login attempt', [
            'email' => $email,
            'ip' => $ip,
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    public static function logUnauthorizedAccess(string $resource, ?int $userId = null): void
    {
        Log::warning('Unauthorized access attempt', [
            'resource' => $resource,
            'user_id' => $userId,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    public static function logSuspiciousActivity(string $activity, array $context = []): void
    {
        Log::alert('Suspicious activity detected', array_merge([
            'activity' => $activity,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ], $context));
    }
}
```

## セキュリティチェックリスト

### 開発時チェック項目
- [ ] すべての入力データにバリデーションを実装
- [ ] すべての出力データを適切にエスケープ
- [ ] CSRFトークンをすべてのフォームに設定
- [ ] 認可チェックをすべてのアクションに実装
- [ ] SQLクエリでパラメータバインディングを使用
- [ ] セキュリティヘッダーを設定
- [ ] 機密情報をログに出力しない

### デプロイ前チェック項目
- [ ] 本番環境でデバッグモードを無効化
- [ ] HTTPS通信を強制
- [ ] セキュリティ関連の環境変数を適切に設定
- [ ] データベースの権限を最小限に設定
- [ ] 不要なファイルやディレクトリを削除

### 定期チェック項目
- [ ] 依存関係の脆弱性スキャン
- [ ] アクセスログの監視
- [ ] セキュリティパッチの適用
- [ ] バックアップの確認

## まとめ

セキュリティは継続的な取り組みです。このガイドラインに従い、常にセキュリティを意識した開発を心がけましょう。
