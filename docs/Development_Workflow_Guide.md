# 開発ワークフローガイド
## バグを防ぐための包括的開発プロセス

### 概要
このガイドは、Laravel + Supabaseプロジェクトでバグを最小限に抑えるための開発ワークフローを定義します。TDD、適切なGitフロー、CI/CDパイプラインを組み合わせた実践的なアプローチを提供します。

## 開発環境セットアップ

### 1. 必要なツール
```bash
# PHP 8.3.6
php --version

# Node.js 22.x
node --version

# Supabase CLI 2.22.12（固定バージョン）
supabase --version

# Composer
composer --version

# Laravel Artisan（必須ツール）
php artisan --version

# Laravel Pint（コードスタイル）
./vendor/bin/pint --version

# PHPStan（静的解析）
./vendor/bin/phpstan --version

# Pest（テストフレームワーク）
./vendor/bin/pest --version
```

### 🛠 重要原則：Artisanコマンド必須
**Laravel開発においては必ず`php artisan`コマンドを使用してファイル及びディレクトリを作成する**

> 📖 **詳細情報**: [Laravel_Artisan_Commands_Guide.md](./Laravel_Artisan_Commands_Guide.md) - Artisanコマンド完全ガイド

### 2. 環境構築手順
```bash
# 1. リポジトリクローン
git clone https://github.com/ShintaroKawakami/jtt-apps.git
cd jtt-apps

# 2. 依存関係インストール
composer install
npm install

# 3. 環境設定
cp .env.example .env.local
php artisan key:generate

# 4. Supabase起動
supabase start

# 5. データベースマイグレーション
php artisan migrate

# 6. 開発サーバー起動
php artisan serve
npm run dev
```

## TDD開発サイクル

### 基本フロー
1. **Red**: 失敗するテストを書く
2. **Green**: テストを通すための最小限のコードを書く
3. **Refactor**: コードを改善する
4. **Commit**: 変更をコミットする

### 実践例：スタッフシフト機能
```bash
# 1. 機能ブランチ作成
git checkout -b feature/staff-shift-management

# 2. テストファイル作成
touch tests/Feature/StaffShiftTest.php
touch tests/Unit/StaffShiftServiceTest.php
```

```php
// tests/Unit/StaffShiftServiceTest.php
<?php

use App\Services\StaffShiftService;
use App\Models\StaffShift;

describe('StaffShiftService', function () {
    beforeEach(function () {
        $this->service = new StaffShiftService();
    });

    // Red: 失敗するテストを書く
    it('creates a new staff shift', function () {
        $shiftData = [
            'staff_id' => 1,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
            'role_col' => 'cashier',
        ];

        $shift = $this->service->createShift($shiftData);

        expect($shift)->toBeInstanceOf(StaffShift::class);
        expect($shift->staff_id)->toBe(1);
        expect($shift->role_col)->toBe('cashier');
    });

    it('validates shift overlap', function () {
        // 既存シフト作成
        StaffShift::factory()->create([
            'staff_id' => 1,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        // 重複するシフトでエラーが発生することを確認
        expect(fn () => $this->service->createShift([
            'staff_id' => 1,
            'start_time' => '2025-01-01 10:00:00',
            'end_time' => '2025-01-01 16:00:00',
            'role_col' => 'cashier',
        ]))->toThrow(ShiftOverlapException::class);
    });
});
```

```bash
# 3. テスト実行（Red）
php artisan test tests/Unit/StaffShiftServiceTest.php
# → 失敗することを確認

# 4. 最小限の実装（Green）
```

```php
// app/Services/StaffShiftService.php
<?php

namespace App\Services;

use App\Models\StaffShift;
use App\Exceptions\ShiftOverlapException;

class StaffShiftService
{
    public function createShift(array $shiftData): StaffShift
    {
        // バリデーション
        $this->validateShiftOverlap($shiftData);

        // シフト作成
        return StaffShift::create($shiftData);
    }

    private function validateShiftOverlap(array $shiftData): void
    {
        $overlapping = StaffShift::where('staff_id', $shiftData['staff_id'])
            ->where(function ($query) use ($shiftData) {
                $query->whereBetween('start_time', [$shiftData['start_time'], $shiftData['end_time']])
                      ->orWhereBetween('end_time', [$shiftData['start_time'], $shiftData['end_time']])
                      ->orWhere(function ($q) use ($shiftData) {
                          $q->where('start_time', '<=', $shiftData['start_time'])
                            ->where('end_time', '>=', $shiftData['end_time']);
                      });
            })
            ->exists();

        if ($overlapping) {
            throw new ShiftOverlapException('シフトが重複しています。');
        }
    }
}
```

```bash
# 5. テスト実行（Green）
php artisan test tests/Unit/StaffShiftServiceTest.php
# → 成功することを確認

# 6. リファクタリング
# コードの改善、最適化

# 7. 全テスト実行
php artisan test

# 8. コードスタイルチェック
./vendor/bin/pint

# 9. 静的解析
./vendor/bin/phpstan analyse

# 10. コミット
git add .
git commit -m "feat: add staff shift management service

- Implement StaffShiftService with overlap validation
- Add comprehensive unit tests
- Handle shift creation and validation logic"
```

## 機能テスト実装

```php
// tests/Feature/StaffShiftTest.php
<?php

use App\Models\User;
use App\Models\StaffShift;

describe('Staff Shift Management', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    it('allows authenticated user to create a shift', function () {
        $response = $this->postJson('/api/staff-shifts', [
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
            'role_col' => 'cashier',
        ]);

        $response->assertStatus(201)
                 ->assertJsonStructure(['id', 'staff_id', 'start_time', 'end_time', 'role_col']);

        $this->assertDatabaseHas('staff_shifts', [
            'staff_id' => $this->user->id,
            'role_col' => 'cashier',
        ]);
    });

    it('validates required fields', function () {
        $response = $this->postJson('/api/staff-shifts', []);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['staff_id', 'start_time', 'end_time', 'role_col']);
    });

    it('prevents overlapping shifts', function () {
        // 既存シフト作成
        StaffShift::factory()->create([
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        // 重複するシフト作成を試行
        $response = $this->postJson('/api/staff-shifts', [
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 10:00:00',
            'end_time' => '2025-01-01 16:00:00',
            'role_col' => 'cashier',
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['start_time']);
    });
});
```

## コントローラー実装

```php
// app/Http/Controllers/Api/StaffShiftController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreStaffShiftRequest;
use App\Services\StaffShiftService;
use Illuminate\Http\JsonResponse;

class StaffShiftController extends Controller
{
    public function __construct(
        private StaffShiftService $staffShiftService
    ) {}

    public function store(StoreStaffShiftRequest $request): JsonResponse
    {
        try {
            $shift = $this->staffShiftService->createShift($request->validated());

            return response()->json($shift, 201);
        } catch (ShiftOverlapException $e) {
            return response()->json([
                'message' => 'バリデーションエラー',
                'errors' => ['start_time' => [$e->getMessage()]]
            ], 422);
        }
    }
}
```

## バリデーション実装

```php
// app/Http/Requests/StoreStaffShiftRequest.php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreStaffShiftRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 認可ロジックを実装
    }

    public function rules(): array
    {
        return [
            'staff_id' => ['required', 'integer', 'exists:users,id'],
            'start_time' => ['required', 'date', 'after:now'],
            'end_time' => ['required', 'date', 'after:start_time'],
            'role_col' => ['required', 'string', 'in:cashier,kitchen,floor'],
        ];
    }

    public function messages(): array
    {
        return [
            'staff_id.required' => 'スタッフを選択してください。',
            'start_time.required' => '開始時間を入力してください。',
            'start_time.after' => '開始時間は現在時刻より後に設定してください。',
            'end_time.required' => '終了時間を入力してください。',
            'end_time.after' => '終了時間は開始時間より後に設定してください。',
            'role_col.required' => '役割を選択してください。',
            'role_col.in' => '有効な役割を選択してください。',
        ];
    }
}
```

## PR作成とレビュープロセス

### 1. PR作成前チェックリスト
```bash
# 単体・機能テスト実行
php artisan test

# E2Eテスト実行（Laravel Dusk）
php artisan dusk

# コードスタイルチェック
./vendor/bin/pint

# 静的解析
./vendor/bin/phpstan analyse

# 型チェック
./vendor/bin/phpstan analyse --level=8
```

### 2. PR作成
```bash
# リモートにプッシュ
git push origin feature/staff-shift-management

# GitHub上でPR作成
# タイトル: feat: add staff shift management system
# 説明テンプレートを使用
```

### 3. PR説明テンプレート
```markdown
## 概要
スタッフシフト管理機能を追加しました。

## 変更内容
- StaffShiftServiceクラスの実装
- シフト重複チェック機能
- API エンドポイントの追加
- バリデーションルールの実装
- 包括的なテストの追加

## テスト
- 単体テスト: StaffShiftServiceTest
- 機能テスト: StaffShiftTest
- E2Eテスト: Laravel Duskによるブラウザテスト
- カバレッジ: 95%以上

## 確認事項
- [x] テストが通ること
- [x] コードスタイルが適切であること
- [x] 静的解析でエラーがないこと
- [x] ドキュメントが更新されていること
```

### 4. CI/CD確認
- GitHub Actionsでテストが通ることを確認
- コードレビューを受ける
- 承認後にマージ

## 継続的改善

### 1. メトリクス監視
- テストカバレッジの維持
- コード品質スコアの向上
- パフォーマンス指標の監視

### 2. 定期的なリファクタリング
- 技術的負債の解消
- パフォーマンス最適化
- セキュリティアップデート

### 3. チーム学習
- コードレビューでの知識共有
- ベストプラクティスの文書化
- 定期的な振り返り会議
