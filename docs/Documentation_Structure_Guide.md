# ドキュメント構造ガイド
## 参照型ファイル構造（Single Source of Truth）の実装

### 概要
このガイドでは、JTT-Appsプロジェクトのドキュメント構造と、新規参画者（AIエージェント・人間開発者）向けの情報取得方法を説明します。

## 🎯 基本原則

### 1. Single Source of Truth（SSOT）
- **1つの情報は1つのファイルにのみ記載**
- 他のファイルからは参照リンクで誘導
- 情報の重複を徹底的に排除

### 2. 階層化された情報構造
```
ルートレベル（基本ガイド）
├── AGENTS.md          # AIエージェント向けマスターガイド
├── CLAUDE.md          # Claude特化ガイド
├── CONTRIBUTING.md    # 貢献者向けガイド
└── README.md          # プロジェクト概要

docsレベル（詳細ガイド）
├── 開発プロセス/
│   ├── Development_Workflow_Guide.md    # 開発ワークフロー（マスター）
│   ├── TDD_Guide.md                     # TDD実践方法
│   └── Git_PR_Guidelines.md             # Git・PR規約
├── 技術仕様/
│   ├── CodingStandards.md               # コーディング規約（マスター）
│   ├── View_Implementation_Guidelines.md # View実装
│   ├── Laravel_Dusk_E2E_Testing.md     # E2Eテスト
│   └── Security_Guidelines.md           # セキュリティ
├── インフラ・DB/
│   ├── Laravel_Supabase_Integration.md  # 統合ガイド（マスター）
│   ├── Supabase_Guidelines.md           # DB設計
│   └── Laravel_Migration_Convention.md  # マイグレーション
├── プロジェクト管理/
│   ├── Project_Management_Guide.md      # プロジェクト管理（マスター）
│   └── CI_CD_Testing.md                 # CI/CD・テスト戦略
└── メタ情報/
    └── Documentation_Structure_Guide.md # このファイル
```

## 📚 情報の一元化マップ

### マスター情報ファイル（Single Source）
| 情報カテゴリ | マスターファイル | 内容 |
|-------------|-----------------|------|
| **開発ワークフロー** | `docs/Development_Workflow_Guide.md` | TDD、ブランチ戦略、PR作成プロセス |
| **コーディング規約** | `docs/CodingStandards.md` | PHP、Laravel、命名規則、スタイル |
| **テスト戦略** | `docs/CI_CD_Testing.md` | テストピラミッド、カバレッジ、CI/CD |
| **セキュリティ** | `docs/Security_Guidelines.md` | XSS、CSRF、認証・認可、RLS |
| **Supabase統合** | `docs/Laravel_Supabase_Integration.md` | 統合方法、RPC、マイグレーション |
| **プロジェクト管理** | `docs/Project_Management_Guide.md` | Issue管理、スプリント、品質管理 |
| **環境要件** | `README.md` | PHP、Node.js、Supabase CLI バージョン |

### 参照ファイル（Reference Only）
| ファイル | 役割 | 参照先 |
|---------|------|--------|
| `AGENTS.md` | AIエージェント向け統合ガイド | 各マスターファイルへのリンク集 |
| `CLAUDE.md` | Claude特化ガイド | 重要なマスターファイルの要約 |
| `CONTRIBUTING.md` | 貢献者向けガイド | 開発プロセスの要約と参照 |

## 🔄 情報更新フロー

### 1. 情報更新の原則
```mermaid
graph TD
    A[情報更新の必要性] --> B{マスターファイルを特定}
    B --> C[マスターファイルを更新]
    C --> D[参照ファイルのリンクを確認]
    D --> E[必要に応じて参照ファイルの要約を更新]
    E --> F[PR作成・レビュー]
    F --> G[マージ後、他の開発者に通知]
```

### 2. 更新手順
1. **マスターファイル特定**: 上記マップで該当するマスターファイルを確認
2. **マスターファイル更新**: 該当ファイルの情報を更新
3. **参照整合性確認**: 他のファイルからの参照リンクが正しいか確認
4. **要約更新**: 必要に応じて参照ファイルの要約部分を更新
5. **Issue作成**: 変更内容をIssueとして記録
6. **PR作成**: 適切なPRテンプレートで変更を提出

## 🆕 新規参画者向けガイド

### AIエージェント向け
1. **最初に読むファイル**: `AGENTS.md`
2. **役割確認**: AIエージェント役割分担表を確認
3. **開発フロー理解**: `docs/Development_Workflow_Guide.md`を熟読
4. **コーディング規約**: `docs/CodingStandards.md`を確認
5. **実践開始**: TDDサイクルに従って開発開始

### 人間開発者向け
1. **プロジェクト概要**: `README.md`でプロジェクト全体を把握
2. **貢献方法**: `CONTRIBUTING.md`で貢献プロセスを理解
3. **環境構築**: `README.md`のクイックスタートを実行
4. **開発ルール**: `docs/Development_Workflow_Guide.md`を熟読
5. **Issue作成**: GitHub Issuesで最初のタスクを作成

### 共通の学習パス
```
1. プロジェクト理解
   └── README.md → CONTRIBUTING.md

2. 開発プロセス理解
   └── docs/Development_Workflow_Guide.md → docs/TDD_Guide.md

3. 技術仕様理解
   └── docs/CodingStandards.md → docs/Security_Guidelines.md

4. 実践開始
   └── Issue作成 → ブランチ作成 → TDD実装
```

## 📝 ドキュメント更新ガイドライン

### 更新が必要なケース
- 新しい技術の導入
- 開発プロセスの変更
- セキュリティ要件の追加
- ツールのバージョンアップ

### 更新手順（詳細）
1. **Issue作成**
   ```markdown
   タイトル: [docs] ドキュメント更新: [更新内容]
   
   ## 更新理由
   なぜこの更新が必要か
   
   ## 更新対象
   - [ ] マスターファイル: docs/xxx.md
   - [ ] 参照ファイル: AGENTS.md（要約部分）
   
   ## 影響範囲
   この更新により影響を受ける他のドキュメント
   ```

2. **ブランチ作成**
   ```bash
   git checkout -b docs/update-[更新内容]
   ```

3. **マスターファイル更新**
   - 該当するマスターファイルを特定
   - 情報を追加・修正・削除
   - 他のファイルに同じ情報がないか確認

4. **参照整合性確認**
   ```bash
   # 参照リンクの確認
   grep -r "docs/更新したファイル名" .
   
   # 重複情報の確認
   grep -r "更新した内容のキーワード" docs/
   ```

5. **要約更新**
   - AGENTS.md、CLAUDE.md、CONTRIBUTING.mdの要約部分を更新
   - 詳細は参照リンクで誘導

6. **PR作成**
   ```markdown
   ## 概要
   Closes #[Issue番号]
   
   ## 変更内容
   - マスターファイル更新: docs/xxx.md
   - 参照ファイル要約更新: AGENTS.md
   
   ## 確認事項
   - [ ] 情報の重複がないこと
   - [ ] 参照リンクが正しいこと
   - [ ] 要約が最新であること
   ```

## 🔍 品質チェックリスト

### ドキュメント品質確認
- [ ] 情報が1つのファイルにのみ存在する
- [ ] 参照リンクが正しく機能する
- [ ] 要約と詳細の整合性が取れている
- [ ] 新規参画者が迷わない構造になっている
- [ ] 更新日時が記載されている

### 定期メンテナンス
- **月次**: 参照リンクの確認
- **四半期**: ドキュメント構造の見直し
- **半年**: 新規参画者フィードバックの反映

## 🚨 よくある間違い

### ❌ やってはいけないこと
1. **情報の重複記載**: 同じ情報を複数ファイルに書く
2. **直接詳細記載**: 参照ファイルに詳細情報を書く
3. **リンク切れ**: 参照リンクのメンテナンス不足
4. **要約の不整合**: マスターファイルと要約の内容が異なる

### ✅ 正しいアプローチ
1. **マスターファイル特定**: 情報の所在を明確にする
2. **参照リンク使用**: 詳細は適切なファイルに誘導
3. **要約の簡潔性**: 参照ファイルは要点のみ記載
4. **定期的な整合性確認**: リンクと内容の確認

## 📞 サポート

### 困ったときの対処法
1. **情報が見つからない**: `AGENTS.md`の目次から探す
2. **どこに書くべきか不明**: このガイドのマップを確認
3. **重複情報を発見**: Issue作成して整理を依頼
4. **リンク切れ発見**: Issue作成して修正を依頼

### 改善提案
ドキュメント構造の改善提案は、以下のテンプレートでIssueを作成：

```markdown
タイトル: [docs] ドキュメント構造改善提案

## 現在の問題
現在のドキュメント構造で困っていること

## 提案内容
どのように改善したいか

## 期待効果
改善により得られる効果
```

---

> 📖 **重要**: このガイドに従うことで、一貫性があり保守しやすいドキュメント体系を維持できます。新規参画者の学習効率も大幅に向上します。
