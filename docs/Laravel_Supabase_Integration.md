# LaravelとSupabaseの統合アプローチ

## 環境要件
- Laravel 12.13
- PHP 8.3.6
- Supabase Pro Tier
- Supabase CLI 2.22.12（固定バージョン）

## 基本方針
- Laravelはフロントエンド・APIを担当
- Supabaseはバックエンドデータストアおよびバックエンドロジックを担当
- 両方のシステムでスキーマやロジックの一貫性を維持

## 実装パターン
1. Laravel → Supabase アクセスパターン:
   - Laravel Service Layer → select * from public.xxx_api(...)
   - 認証情報を適切に管理し、サービスロールは必要な場合のみ使用

2. データフロー:
   - READ操作: Laravel → public.*_api → core.* (テーブル)
   - WRITE操作: Laravel → public.*_api → core.* (insert/update/delete)

3. スキーマ同期:
   - Laravel Migrationでスキーマをバージョン管理
   - Supabaseマイグレーションスクリプトも同時に管理
   - CI/CDパイプラインで両方に適用

## セキュリティ原則
- core.*テーブルへの直接アクセスはサービスロールのみ
- public関数のバリデーションで不正アクセスを防止
- 認証トークンの適切な管理