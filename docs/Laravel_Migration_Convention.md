# Laravelマイグレーション規約

## 命名規則
- テーブル作成: create_{テーブル名}_table
- テーブル変更: update_{テーブル名}_add_{カラム名}
- リレーション追加: create_{テーブル1}_{テーブル2}_relation

## 構造
- upとdownメソッドを必ず実装する
- downメソッドはupの操作を完全に元に戻せるようにする
- インデックスはパフォーマンスを考慮して適切に設定する

## 外部キー
- 外部キー制約は明示的に定義する
- on_deleteとon_updateの動作を指定する (例: cascade, restrict)

## タイムスタンプとソフトデリート
- timestamps()メソッドを使用して作成・更新日時を管理
- softDeletes()を使用して論理削除を実装

## Supabaseとの連携
- LaravelマイグレーションとSupabaseの同期方法を決める
  (例: マイグレーション後にSupabaseへの適用スクリプトを実行)
- Supabaseのマイグレーションファイル命名規則(YYYYMMDDHHMM_<description>.sql)と整合性を保つ