-- プロシージャの作成
-- get_staff_email_api (既に実装済み)

-- get_today_shift_api (既に実装済み)

-- get_store_info_api
CREATE OR REPLACE FUNCTION public.get_store_info_api(code text DEFAULT 'orielle'::text)
RETURNS TABLE (
    id uuid,
    name text,
    address text,
    phone text,
    email text,
    opening_hours jsonb,
    google_map_url text,
    floor_info text,
    notes text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY SELECT 
        s.id, 
        s.name, 
        s.address, 
        s.phone, 
        s.email, 
        s.opening_hours, 
        s.google_map_url, 
        s.floor_info,
        s.notes,
        s.created_at,
        s.updated_at
    FROM public.store_info s
    LIMIT 1;
END;
$$;

-- get_special_opening_hours_api
CREATE OR REPLACE FUNCTION public.get_special_opening_hours_api(p_code text, p_from date DEFAULT NULL::date, p_to date DEFAULT NULL::date)
RETURNS TABLE(
    date date,
    opening_time time without time zone,
    closing_time time without time zone,
    is_closed boolean,
    note text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 開店時間のプレースホルダーデータを返す
    RETURN QUERY
    VALUES 
        (CURRENT_DATE, '10:00'::time, '22:00'::time, false, '通常営業'),
        (CURRENT_DATE + 1, '10:00'::time, '22:00'::time, false, '通常営業'),
        (CURRENT_DATE + 2, '10:00'::time, '22:00'::time, false, '通常営業'),
        (CURRENT_DATE + 3, '10:00'::time, '22:00'::time, false, '通常営業'),
        (CURRENT_DATE + 4, NULL, NULL, true, '臨時休業');
END;
$$;

-- get_menu_items_api
CREATE OR REPLACE FUNCTION public.get_menu_items_api(store_code text, category_filter text DEFAULT NULL::text, only_available boolean DEFAULT true, only_published boolean DEFAULT true)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    price integer,
    category text,
    image_url text,
    is_seasonal boolean,
    is_available boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.name,
        m.description,
        m.price,
        m.category,
        m.image_url,
        m.is_seasonal,
        m.is_available,
        m.created_at,
        m.updated_at
    FROM 
        public.menu_items m
    WHERE 
        (category_filter IS NULL OR m.category = category_filter)
        AND (NOT only_available OR m.is_available = true)
        AND (NOT only_published OR true);
END;
$$;

-- list_menu_items_api
CREATE OR REPLACE FUNCTION public.list_menu_items_api(p_category_filter text DEFAULT NULL::text, p_available_filter boolean DEFAULT NULL::boolean)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    price integer,
    category text,
    image_url text,
    is_seasonal boolean,
    is_available boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.name,
        m.description,
        m.price,
        m.category,
        m.image_url,
        m.is_seasonal,
        m.is_available,
        m.created_at,
        m.updated_at
    FROM 
        public.menu_items m
    WHERE 
        (p_category_filter IS NULL OR m.category = p_category_filter)
        AND (p_available_filter IS NULL OR m.is_available = p_available_filter);
END;
$$;

-- set_timestamp
CREATE OR REPLACE FUNCTION public.set_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- トリガーの作成
CREATE TRIGGER set_timestamp_categories_table
BEFORE UPDATE ON public.categories
FOR EACH ROW
EXECUTE FUNCTION public.set_timestamp();

CREATE TRIGGER set_timestamp_menu_items_table
BEFORE UPDATE ON public.menu_items
FOR EACH ROW
EXECUTE FUNCTION public.set_timestamp();

CREATE TRIGGER set_timestamp_store_info_table
BEFORE UPDATE ON public.store_info
FOR EACH ROW
EXECUTE FUNCTION public.set_timestamp();

CREATE TRIGGER set_timestamp_website_settings_table
BEFORE UPDATE ON public.website_settings
FOR EACH ROW
EXECUTE FUNCTION public.set_timestamp();

CREATE TRIGGER set_timestamp_reservations_table
BEFORE UPDATE ON public.reservations
FOR EACH ROW
EXECUTE FUNCTION public.set_timestamp();

-- プレースホルダーの必要最小限の関数
CREATE OR REPLACE FUNCTION public.format_notification_api(p_operation_type text, p_data json)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN 'Notification: ' || p_operation_type || ' - ' || p_data::text;
END;
$$;

-- 権限付与
GRANT EXECUTE ON FUNCTION public.get_menu_items_api TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_special_opening_hours_api TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_store_info_api TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.list_menu_items_api TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.format_notification_api TO anon, authenticated, service_role;