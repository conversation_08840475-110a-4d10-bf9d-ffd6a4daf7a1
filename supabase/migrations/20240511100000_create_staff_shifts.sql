-- Create the core schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS core;

-- Create staff_shifts table in core schema
CREATE TABLE IF NOT EXISTS core.staff_shifts (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    store_id BIGINT NOT NULL,
    staff_id UUID NOT NULL,
    role_col TEXT NOT NULL,
    start_at TIMESTAMP WITH TIME ZONE NOT NULL,
    end_at TIMESTAMP WITH TIME ZONE NOT NULL,
    note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_staff_shifts_store_staff ON core.staff_shifts(store_id, staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_shifts_timerange ON core.staff_shifts(start_at, end_at);

-- Enable Row Level Security
ALTER TABLE core.staff_shifts ENABLE ROW LEVEL SECURITY;

-- Create policy allowing service role to perform all operations
CREATE POLICY service_role_policy ON core.staff_shifts
    USING (auth.role() = 'service_role');

-- Create function to get today's shifts
CREATE OR REPLACE FUNCTION core.get_today_shift(p_staff_id UUID)
RETURNS SETOF core.staff_shifts
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM core.staff_shifts
    WHERE staff_id = p_staff_id
      AND date(start_at) = CURRENT_DATE
      AND deleted_at IS NULL
    ORDER BY start_at ASC;
$$;

-- Create public API wrapper for the function
CREATE OR REPLACE FUNCTION public.get_today_shift_api(p_staff_id UUID)
RETURNS SETOF core.staff_shifts
LANGUAGE sql
AS $$
    SELECT * FROM core.get_today_shift(p_staff_id);
$$;

-- Grant execute permission to authenticated and anonymous users
GRANT EXECUTE ON FUNCTION public.get_today_shift_api TO authenticated, anon;
