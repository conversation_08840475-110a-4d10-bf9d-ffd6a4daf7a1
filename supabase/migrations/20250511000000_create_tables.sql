-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id uuid DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    display_order integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create the menu_items table
CREATE TABLE IF NOT EXISTS public.menu_items (
    id uuid DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    description text,
    price integer NOT NULL,
    category text NOT NULL,
    image_url text,
    is_seasonal boolean DEFAULT false,
    is_available boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create the contact_messages table
CREATE TABLE IF NOT EXISTS public.contact_messages (
    id uuid DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    email text NOT NULL,
    phone text,
    message text NOT NULL,
    is_read boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);

-- Create the website_settings table
CREATE TABLE IF NOT EXISTS public.website_settings (
    site_id text NOT NULL PRIMARY KEY,
    site_name text NOT NULL,
    domain text,
    theme jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create the store_info table
CREATE TABLE IF NOT EXISTS public.store_info (
    id uuid DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    name text NOT NULL,
    address text NOT NULL,
    phone text NOT NULL,
    email text,
    opening_hours jsonb NOT NULL,
    google_map_url text,
    floor_info text,
    notes text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create the reservations table
CREATE TABLE IF NOT EXISTS public.reservations (
    id uuid DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    customer_name text NOT NULL,
    email text NOT NULL,
    phone text,
    date date NOT NULL,
    time time without time zone NOT NULL,
    party_size integer NOT NULL,
    special_requests text,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.website_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.store_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reservations ENABLE ROW LEVEL SECURITY;

-- Create policies for categories
CREATE POLICY "匿名ユーザーはカテゴリーを閲覧可能" ON public.categories
    FOR SELECT
    USING (true);

-- Create policies for menu_items
CREATE POLICY "menu_items_admin_all" ON public.menu_items
    FOR ALL
    USING (((auth.jwt() ->> 'role'::text) = 'authenticated'::text));

CREATE POLICY "menu_items_anon_select" ON public.menu_items
    FOR SELECT
    USING (true);

CREATE POLICY "匿名ユーザーはメニュー項目を閲覧可能" ON public.menu_items
    FOR SELECT
    USING (true);

-- Create policies for contact_messages
CREATE POLICY "匿名ユーザーはお問い合わせを送信可能" ON public.contact_messages
    FOR INSERT
    WITH CHECK (true);

-- Create policies for store_info
CREATE POLICY "匿名ユーザーは店舗情報を閲覧可能" ON public.store_info
    FOR SELECT
    USING (true);

-- Create policies for website_settings
CREATE POLICY "匿名ユーザーはサイト設定を閲覧可能" ON public.website_settings
    FOR SELECT
    USING (true);

-- Create policies for reservations
CREATE POLICY "reservations_admin_all" ON public.reservations
    FOR ALL
    USING (((auth.jwt() ->> 'role'::text) = 'authenticated'::text));

CREATE POLICY "reservations_anon_insert" ON public.reservations
    FOR INSERT
    WITH CHECK (true);