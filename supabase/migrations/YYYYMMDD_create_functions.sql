CREATE OR REPLACE FUNCTION public.get_today_shift_api(p_staff_id UUID)
RETURNS TABLE(
    id BIGINT,
    store_id BIGINT,
    staff_id UUID,
    role_col TEXT,
    start_at TIMESTAMP WITH TIME ZONE,
    end_at TIMESTAMP WITH TIME ZONE,
    note TEXT
)
LANGUAGE sql
AS $$
    -- 関数の実装内容
$$;

-- 条件付きGRANT: 権限が既に存在するかチェックして重複を防止
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_proc
    WHERE pronamespace = 'public'::regnamespace
      AND proname = 'get_today_shift_api'
  ) THEN
    GRANT EXECUTE ON FUNCTION public.get_today_shift_api TO anon, authenticated, service_role;
  END IF;
END $$;
