-- Create a function to get staff email by full name
CREATE OR REPLACE FUNCTION public.get_staff_email_api(p_full_name text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This is a placeholder function that would normally query your staff database
    -- In production, you would implement logic to look up staff emails by name
    
    -- Basic example mapping logic - Replace this with your actual staff data
    RETURN CASE
        WHEN p_full_name = 'スタッフ1' THEN '<EMAIL>'
        WHEN p_full_name = 'スタッフ2' THEN '<EMAIL>'
        WHEN p_full_name = '店長' THEN '<EMAIL>'
        ELSE NULL
    END;
END;
$$;

-- Grant access to get_staff_email_api conditionally
DO $$
BEGIN
  IF NOT EXISTS (
      SELECT 1
      FROM information_schema.routine_privileges
      WHERE routine_schema = 'public'
        AND routine_name   = 'get_staff_email_api'
        AND grantee        = 'anon') THEN
    GRANT EXECUTE ON FUNCTION public.get_staff_email_api(text)
          TO anon, authenticated, service_role;
  END IF;
END $$;

-- Create a function to get today's shift information
CREATE OR REPLACE FUNCTION public.get_today_shift_api(p_store text)
RETURNS TABLE (
    name text,
    role text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This is a placeholder function that would normally query your shift database
    -- In production, you would implement logic to look up shift information for the current day
    
    -- Return placeholder data
    RETURN QUERY
    VALUES 
        ('店長', '店長'),
        ('スタッフ1', '社員'),
        ('スタッフ2', 'リーダー');
END;
$$;

-- Grant access to get_today_shift_api conditionally
DO $$
BEGIN
  IF NOT EXISTS (
      SELECT 1
      FROM information_schema.routine_privileges
      WHERE routine_schema = 'public'
        AND routine_name   = 'get_today_shift_api'
        AND grantee        = 'anon') THEN
    GRANT EXECUTE ON FUNCTION public.get_today_shift_api(text)
          TO anon, authenticated, service_role;
  END IF;
END $$;