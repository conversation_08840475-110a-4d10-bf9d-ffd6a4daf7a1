import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { google } from "https://esm.sh/googleapis@130?bundle";

// ────────────────────────────────────────────────────────────────────────────
// 1. 環境変数
// ────────────────────────────────────────────────────────────────────────────
const env = Deno.env.toObject();
const {
  ASANA_ACCESS_TOKEN,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  WORKSPACE_ID,
  PROJECT_ID,
  ORDER_FORM_ID,
  SHOPPING_FORM_ID,
  ORDER_SHEET_ID,
  SHOPPING_SHEET_ID,
  GOOGLE_SERVICE_ACCOUNT,
  STORE_NAME, // 店舗名の環境変数を追加
} = env;

// 店舗名のデフォルト値を設定
// STORE_NAMEが設定されていない場合は「御徒町店」を使用
const storeName = STORE_NAME || "御徒町店";

// ────────────────────────────────────────────────────────────────────────────
// 2. SDK 初期化
// ────────────────────────────────────────────────────────────────────────────

// Asana API
async function callAsana(endpoint: string, method = 'GET', data = null) {
  const url = `https://app.asana.com/api/1.0${endpoint}`;
  const options: RequestInit = {
    method,
    headers: {
      'Authorization': `Bearer ${ASANA_ACCESS_TOKEN}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  };
  
  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }
  
  const response = await fetch(url, options);
  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(`Asana API error: ${result.errors?.[0]?.message || response.statusText}`);
  }
  
  return result.data;
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: { autoRefreshToken: false, persistSession: false },
});

// Google Sheets
const auth = new google.auth.GoogleAuth({
  credentials: JSON.parse(GOOGLE_SERVICE_ACCOUNT),
  scopes: ["https://www.googleapis.com/auth/spreadsheets"],
});
const sheets = google.sheets({ version: "v4", auth });

// ────────────────────────────────────────────────────────────────────────────
// 3. ユーティリティ関数
// ────────────────────────────────────────────────────────────────────────────
const NO_COMMENTS = "特に無し";

function formatDate(d = new Date()) {
  return d.toISOString().replace("T", " ").substring(0, 19);
}

async function getStaffEmail(fullName: string): Promise<string | null> {
  const { data, error } = await supabase.rpc("get_staff_email_api", {
    p_full_name: fullName,
  });
  if (error) {
    console.error("Supabase RPC error", error);
    return null;
  }
  return data ?? null;
}

// シフト情報を取得する関数
async function getTodayShift() {
  // 規約に従って関数名とパラメータを変更
  const { data, error } = await supabase.rpc("get_today_shift_api", {
    p_store: storeName, // p_dateからp_storeに変更
  });
  
  if (error) {
    console.error("Error fetching shift data:", error);
    return []; // エラー時は空配列を返す
  }
  
  return data || [];
}

// シートにデータを追加する関数
async function appendSheet(sheetId: string, sheetName: string, row: string[]) {
  try {
    await sheets.spreadsheets.values.append({
      spreadsheetId: sheetId,
      range: `${sheetName}!A:F`,
      valueInputOption: "USER_ENTERED",
      insertDataOption: "INSERT_ROWS",
      requestBody: {
        values: [row],
      },
    });
  } catch (error) {
    console.error("Google Sheets API error:", error);
    throw error;
  }
}

// 1. シフト内から優先度順に担当者を決定
function selectAssignee(
  shift: Array<{ name: string; role: string }>,
): string | null {
  // 優先順位: 店長 > 社員 > リーダー > アルバイト
  const priority = ["店長", "社員", "リーダー"];
  for (const r of priority) {
    const p = shift.find((s) => s.role === r);
    if (p) return p.name;
  }
  // 該当なしなら最初の人でも可
  return shift[0]?.name ?? null;
}

// 2. createAsanaTask を汎用化
async function createAsanaTask(
  name: string,
  dueOn: string,
  notes: string,
  requesterEmail: string | null,
  shift: Array<{ name: string; role: string }>,
) {
  // ── 担当者決定 ─────────────────────────────
  const assigneeName = selectAssignee(shift);
  const assignee = assigneeName
    ? await getStaffEmail(assigneeName)
    : undefined;

  // ── コラボレーター ─────────────────────────
  const followers = new Set<string>();
  if (requesterEmail) followers.add(requesterEmail);

  // シフトに入っている社員／リーダー全員をフォロワーに追加
  for (const p of shift) {
    if (["社員", "リーダー"].includes(p.role)) {
      const mail = await getStaffEmail(p.name);
      if (mail) followers.add(mail);
    }
  }

  // AsanaのAPIを直接呼び出し
  const taskData = {
    workspace: WORKSPACE_ID,
    projects: [PROJECT_ID],
    name,
    notes,
    due_on: dueOn,
  };
  
  if (assignee) {
    taskData.assignee = assignee;
  }
  
  const task = await callAsana('/tasks', 'POST', { data: taskData });
  
  // フォロワーを追加
  const followersList = [...followers];
  if (followersList.length > 0) {
    for (const follower of followersList) {
      try {
        await callAsana(`/tasks/${task.gid}/addFollowers`, 'POST', {
          data: { followers: [follower] }
        });
      } catch (error) {
        console.error(`Failed to add follower ${follower}:`, error);
      }
    }
  }
  
  return task;
}

// ────────────────────────────────────────────────────────────────────────────
// 4. JotForm 解析
// ────────────────────────────────────────────────────────────────────────────
interface Item {
  productName: string;
  quantity: string;
  desiredDate: string;
  notes: string;
}
interface Parsed {
  requesterName: string;
  items: Item[];
  kind: "order" | "shopping";
}

function parseJotForm(payload: Record<string, unknown>): Parsed {
  const formID = payload.formID as string;
  const pretty = payload.pretty as string;

  const requesterName =
    (pretty.match(/依頼者:([^,]*)|発注者名:([^,]*)/)?.[1] ?? "").trim();

  // 本番は発注／買物で正規表現を分けるほうが安全
  const itemsJson = pretty.match(/\[(\{.*?\})\]/)?.[1] ?? "{}";
  const items: Item[] = JSON.parse(`[${itemsJson}]`);

  return {
    requesterName,
    items: items.map((it) => ({
      productName: it["商品名"],
      quantity: it["個数"],
      desiredDate: it["希望納期"] || new Date().toISOString().slice(0, 10),
      notes: it["備考"] || NO_COMMENTS,
    })),
    kind: formID === ORDER_FORM_ID ? "order" : "shopping",
  };
}

// ────────────────────────────────────────────────────────────────────────────
// 5. Edge Function エントリポイント
// ────────────────────────────────────────────────────────────────────────────
serve(async (req) => {
  if (req.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  try {
    const body = await req.json();
    const parsed = parseJotForm(body);
    const shift = await getTodayShift();
    const requesterEmail = await getStaffEmail(parsed.requesterName);

    for (const i of parsed.items) {
      const prefix = parsed.kind === "order" ? "【発注依頼】" : "【ハナマサ買物依頼】";
      const taskName =
        `${prefix} ${i.productName} ${i.quantity}個, ${i.desiredDate}` +
        (i.notes !== NO_COMMENTS ? ", 備考あり❗❗" : "");

      await createAsanaTask(
        taskName,
        i.desiredDate,
        `依頼者:${parsed.requesterName}\n商品名:${i.productName}\n個数:${i.quantity}\n希望納期:${i.desiredDate}\n\n備考:${i.notes}`,
        requesterEmail,
        shift,
      );

      const row = [
        formatDate(),
        parsed.requesterName,
        i.productName,
        i.quantity,
        i.desiredDate,
        i.notes,
      ];
      const sheetId = parsed.kind === "order" ? ORDER_SHEET_ID : SHOPPING_SHEET_ID;
      const sheetName = parsed.kind === "order" ? "発注データ" : "買物データ";
      await appendSheet(sheetId, sheetName, row);
    }

    return new Response(JSON.stringify({ status: "success" }), { status: 201 });
  } catch (e) {
    console.error("Edge Function error", e);
    return new Response(JSON.stringify({ status: "error", message: e.message }), {
      status: 500,
    });
  }
});