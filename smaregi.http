### 環境変数
@auth_url = https://id.smaregi.jp
@api_url = https://api.smaregi.jp
@contract_id = sya050n2
@client_id = e1d47d64a928dfe9076561fb2a13f48c
@client_secret = 440aecb75bf6a3ebea21fca02577dd70022e8f2424f3a499616bcbcd5390f813
@scope = pos.stores:read


### 1) トークン取得
# @name getToken
POST {{auth_url}}/app/{{contract_id}}/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id={{client_id}}&
client_secret={{client_secret}}&
scope={{scope}}

> {%
    client.global.set("bearer", response.body.access_token);
%}


### 2) 店舗一覧取得
GET {{api_url}}/{{contract_id}}/pos/stores
Authorization: Bearer {{bearer}}
Accept: application/json
