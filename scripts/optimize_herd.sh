#!/bin/bash
#----------------------------------------------------------------
# Herd の FPM 常駐化＋ウォームアップ自動化スクリプト
#   1) php-fpm 設定を dynamic / 常駐 2 プロセスへ変更
#   2) Herd を再起動
#   3) ping でウォームアップ & 所要時間計測
#   4) 結果を echo で要約
#----------------------------------------------------------------
# ※ Herd の PHP バージョンフォルダは
#    ~/Library/Containers/io.roadrunner.herd/Data/php/<version>
#    となっている前提です。複数ある場合は最新を自動取得します。
#----------------------------------------------------------------

#=== 1. 基本変数を取得 ===#
# PHP バージョンフォルダを自動検出
PHP_DIR=$(ls -1d ~/Library/Containers/io.roadrunner.herd/Data/php/* | sort -V | tail -n1)
CFG_FILE="$PHP_DIR/etc/php-fpm.d/www.conf"
BACKUP="$CFG_FILE.bak_$(date +%Y%m%d%H%M%S)"
APP_URL="http://jtt-apps.test"

echo "PHP設定ファイル: $CFG_FILE"
echo "バックアップ: $BACKUP"

#=== 2. 元ファイルをバックアップ ===#
sudo cp "$CFG_FILE" "$BACKUP"
echo "バックアップを作成しました"

#=== 3. pm 設定を書き換え ===#
# pm を dynamic に、子プロセスを常駐 2 に設定
sudo sed -i '' \
    -e 's/^pm *= *.*/pm = dynamic/' \
    -e 's/^pm\.start_servers *= *.*/pm.start_servers = 2/' \
    -e 's/^pm\.max_children *= *.*/pm.max_children = 10/' \
    -e 's/^pm\.min_spare_servers *= *.*/pm.min_spare_servers = 1/' \
    -e 's/^pm\.max_spare_servers *= *.*/pm.max_spare_servers = 3/' \
    "$CFG_FILE"

echo "PHP-FPM設定を更新しました:"
echo "  pm = dynamic"
echo "  pm.start_servers = 2"
echo "  pm.max_children = 10"
echo "  pm.min_spare_servers = 1"
echo "  pm.max_spare_servers = 3"

#=== 4. Herd を再起動 ===#
echo "Herdを再起動しています..."
launchctl kickstart -k gui/$(id -u)/dev.herd.herd.php-fpm.plist || \
    echo "警告: PHP-FPM の再起動に失敗しました。手動で Herd アプリを再起動してください。"

#=== 5. ウォームアップと所要時間計測 ===#
echo "サイトをウォームアップ中..."
sleep 2 # 再起動完了を待つ

WARMUP_START=$(date +%s)
HTTP_STATUS=$(curl -4 -s -o /dev/null -w "%{http_code}" "$APP_URL/ping.txt")
CURL_STATUS=$?
WARMUP_END=$(date +%s)
WARMUP_TIME=$((WARMUP_END - WARMUP_START))

#=== 6. 結果を表示 ===#
echo ""
echo "======== 最適化完了 ========="
echo "PHP-FPM設定: dynamic (常時2プロセス)"
echo "場所: $CFG_FILE"
echo "バックアップ: $BACKUP"
echo "ウォームアップ所要時間: ${WARMUP_TIME}秒"

if [ $CURL_STATUS -eq 0 ] && [ $HTTP_STATUS -eq 200 ]; then
    echo "ステータス: 正常 ✅"
    echo "応答時間: $(curl -4 -s -o /dev/null -w "%{time_connect} / %{time_total}" "$APP_URL/ping.txt") 秒 (接続/合計)"
else
    echo "ステータス: エラー ❌ (HTTP: $HTTP_STATUS, Curl: $CURL_STATUS)"
    echo "確認してください: $APP_URL/ping.txt"
fi
echo "============================"