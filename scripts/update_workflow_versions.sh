#\!/usr/bin/env bash
set -e
PHP_VER=$(grep '^php ' .tool-versions | cut -d' ' -f2 | cut -d'.' -f1-2)
NODE_VER=$(grep '^nodejs ' .tool-versions | cut -d' ' -f2 | cut -d'.' -f1-2)
for f in $(grep -rl '{{PHP_VERSION}}' .github/workflows || true); do
  sed -i '' "s/{{PHP_VERSION}}/${PHP_VER}/g" "$f"
  sed -i '' "s/{{NODE_VERSION}}/${NODE_VER}/g" "$f"
done
sed -i '' "s/{{SUPABASE_CLI_VERSION}}/2.22.12/g" $(grep -rl '{{SUPABASE_CLI_VERSION}}' .github/workflows || true)
echo '✔︎ workflow 更新完了'
