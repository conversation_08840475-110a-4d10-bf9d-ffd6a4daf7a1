#\!/usr/bin/env bash
set -e
PHP_VER=$(grep '^php ' .tool-versions | cut -d' ' -f2 | cut -d'.' -f1-2)
NODE_VER=$(grep '^nodejs ' .tool-versions | cut -d' ' -f2 | cut -d'.' -f1-2)
LARA_VER=$(jq -r '.require."laravel/framework"' composer.json | sed 's/^\\^//')
SUPA_VER=2.22.12
find docs -type f \( -name '*.md' -o -name '*.html' \) |
while read f; do
  sed -Ei "s/{{PHP_VERSION}}/${PHP_VER}/g;
           s/{{NODE_VERSION}}/${NODE_VER}/g;
           s/{{LARAVEL_VERSION}}/${LARA_VER}/g;
           s/{{SUPABASE_CLI_VERSION}}/${SUPA_VER}/g" "$f"
done
echo '✔︎ docs 更新完了'
