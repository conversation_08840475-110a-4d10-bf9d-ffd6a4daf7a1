# JTT-Apps コーディング標準 (v1.0.0)
version: 1.0.0
last_updated: "2025-05-12"
frameworks:
  laravel:
    version: "12.13.0"
    min_php: "8.2"
    support_end: "2027-02-24"
languages:
  php:
    version: "8.2"
    style: "PSR-12"
    indent: 4
    max_line_length: 120
  javascript:
    version: "ES2022"
    style: "prettier"
    indent: 2
architecture:
  pattern: "layered"
  dependencies:
    - "Controller -> Service -> Model -> DB"
  naming:
    classes: "PascalCase"
    methods: "camelCase"
    variables: "camelCase"
    constants: "SNAKE_CASE_CAPS"
    private_props: "camelCase"
    boolean_methods: "is*, has*, can*"
tools:
  testing:
    - "phpunit"
    - "larastan"
    - "playwright"
  ci:
    - "GitHub Actions"
  local:
    - "supabase-cli"
